from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

from cortexacommon.config import KafkaSettings, SessionAudioSettings


class Settings(BaseSettings):
    """Service-specific settings."""

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    audio: SessionAudioSettings = Field(default_factory=SessionAudioSettings)
    # AI service-specific settings
    service_ip: str = Field(default="ai", description="IP address of the AI service as seen by the Voice Router")
    voice_router_url: str = Field(default="http://localhost:3001", description="Voice Router URL")

    # Shared settings
    kafka: KafkaSettings = Field(default_factory=KafkaSettings)
    
    # Provider Selection Configuration
    stt_provider: str = Field(
        default="whisper",
        description="STT provider (whisper)"
    )
    tts_provider: str = Field(
        default="openai",
        description="TTS provider (openai)"
    )
    translation_provider: str = Field(
        default="huggingface",
        description="Translation provider (huggingface, google, etc.)"
    )
    vad_provider: str = Field(
        default="webrtc",
        description="VAD provider (silero, webrtc)"
    )

    # Whisper STT Configuration
    whisper_model_size: str = Field(
        default="medium.en",
        description="Whisper model size (tiny, base, small, medium, large)"
    )
    whisper_compute_type: str = Field(
        default="int8",
        description="Whisper compute type (int8, int16, float16, float32)"
    )
    whisper_device: str = Field(
        default="cpu",
        description="Device for Whisper model (cpu, cuda)"
    )

    # VAD Configuration
    vad_aggressiveness: int = Field(
        default=3,
        ge=0,
        le=3,
        description="VAD aggressiveness level (0-3)"
    )
    vad_frame_duration_ms: int = Field(
        default=20,
        description="VAD frame duration in milliseconds"
    )

    # Translation Configuration
    translation_model: str = Field(
        default="Helsinki-NLP/opus-mt-en-es",
        description="Translation model identifier"
    )
    translation_device: str = Field(
        default="cpu",
        description="Device for translation model (cpu, cuda)"
    )

    # OpenAI TTS Configuration
    tts_model: str = Field(
        default="tts-1",
        description="TTS model identifier"
    )
    tts_voice: str = Field(
        default="alloy",
        description="TTS voice identifier"
    )
    tts_api_key: str | None = Field(
        default=None,
        description="API key for TTS provider"
    )


# Global settings instance
settings = Settings()
