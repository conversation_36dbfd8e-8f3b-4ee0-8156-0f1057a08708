from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker

from .models import Call


class CallRepository:
    def __init__(self, session_factory: async_sessionmaker[AsyncSession]):
        self._session_factory = session_factory

    async def get(self, call_id: str) -> Call | None:
        async with self._session_factory() as session:
            result = await session.execute(select(Call).where(Call.id == call_id))
            return result.scalar_one_or_none()

    async def create_if_not_exists(self, call_id: str) -> Call:
        async with self._session_factory() as session:
            async with session.begin():
                result = await session.execute(select(Call).where(Call.id == call_id))
                existing = result.scalar_one_or_none()
                if existing:
                    return existing
                call = Call(id=call_id)
                session.add(call)
                await session.flush()
                return call

    async def set_media_session(self, call_id: str, media_session_id: str) -> None:
        async with self._session_factory() as session:
            async with session.begin():
                await session.execute(
                    update(Call).where(Call.id == call_id).values(media_session_id=media_session_id)
                )

    async def set_status(self, call_id: str, status: str) -> None:
        async with self._session_factory() as session:
            async with session.begin():
                await session.execute(
                    update(Call).where(Call.id == call_id).values(status=status)
                )

    async def set_operator(self, call_id: str, operator_id: str) -> None:
        async with self._session_factory() as session:
            async with session.begin():
                await session.execute(
                    update(Call).where(Call.id == call_id).values(operator_id=operator_id)
                )

    async def get_by_media_session_id(self, media_session_id: str) -> Call | None:
        async with self._session_factory() as session:
            result = await session.execute(
                select(Call).where(Call.media_session_id == media_session_id))
            return result.scalars().first()
