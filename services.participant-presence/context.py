
from dataclasses import dataclass

from config import settings
from core.participant import ParticipantRepository

from cortexacommon.events.producer import EventProducer


from dataclasses import dataclass


@dataclass
class ApplicationContext:
    """
    Application context for managing shared resources.
    """

    event_producer: EventProducer
    participant_repository: ParticipantRepository

    @classmethod
    async def create(cls) -> 'ApplicationContext':
        try:
            event_producer = EventProducer(settings.kafka_settings)
            await event_producer.start()

            participant_repository = ParticipantRepository()

            return cls(
                event_producer=event_producer,
                participant_repository=participant_repository,
            )
        except Exception as e:
            raise RuntimeError(f"Failed to initialize application context: {e}")

    async def cleanup(self) -> None:
        """Clean up the application context and its resources."""
        try:
            if self.event_producer:
                await self.event_producer.stop()
        except Exception as e:
            print(f"Error during application context cleanup: {e}")
