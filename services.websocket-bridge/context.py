from cortexacommon.events.producer import EventProducer
from dataclasses import dataclass

from cortexacommon.logging import get_logger
from cortexacommon.security import Security

from config import settings
from services.connection_manager import ConnectionManager
from services.event_handler import KafkaEventRouter


logger = get_logger(__name__)


from dataclasses import dataclass


@dataclass
class ApplicationContext:
    connection_manager: ConnectionManager
    security: Security
    router: KafkaEventRouter | None

    @classmethod
    async def create(cls) -> "ApplicationContext":
        connections = ConnectionManager()
        security = Security(settings.security)
        router = KafkaEventRouter(
            connections,
            settings.kafka_settings,
            topics=["call.assigned", "webrtc.connection.details", "transcription.completed"],
        )
        await router.start()
        logger.info("WebSocket bridge initialized and consuming events")
        return cls(connection_manager=connections, security=security, router=router)

    async def cleanup(self) -> None:
        if self.router:
            await self.router.stop()
            self.router = None
