import asyncio
from typing import Dict

from cortexacommon.logging import get_logger
from cortexacommon.forking.session import AudioForkingSession
from cortexacommon.clients.voice_router import VoiceRouterClient
from cortexacommon.audio.stream import AudioStream
from cortexacommon.audio.handler import BaseAudioPacketHandler
from cortexacommon.config import SessionAudioSettings


logger = get_logger(__name__)


class Session:
    """
    Manages the full lifecycle of a single call's media forking.
    """

    def __init__(
        self,
        call_id: str,
        media_session_id: str,
        participants: Dict[str, str],
        handler: BaseAudioPacketHandler,
        voice_router_client: VoiceRouterClient,
        audio_settings: SessionAudioSettings,
        service_ip: str,
    ):
        self.call_id = call_id
        self.media_session_id = media_session_id
        self.participants = participants
        self.handler = handler
        self._forking_session = AudioForkingSession(
            service_ip=service_ip, voice_router_client=voice_router_client
        )
        self._audio_settings = audio_settings
        self._audio_streams: Dict[str, AudioStream] = {}
        self._is_running = False

    def _on_participant_packet(self, participant_id: str, payload: bytes) -> None:
        """Callback from AudioForkingSession to handle an incoming RTP packet."""
        stream = self._audio_streams.get(participant_id)
        if not stream:
            logger.warning(
                f"No audio stream for participant {participant_id} in call {self.call_id}"
            )
            return
        asyncio.create_task(stream.process_packet(payload))

    async def start(self) -> None:
        """Initializes audio streams and starts the media forking session."""
        if self._is_running:
            return

        for participant_id in self.participants.values():
            self._audio_streams[participant_id] = AudioStream(
                handler=self.handler,
                participant_id=participant_id,
                call_id=self.call_id,
                audio_settings=self._audio_settings,
            )

        await self._forking_session.start(
            media_session_id=self.media_session_id,
            labels=list(self.participants.keys()),
            on_participant_packet=self._on_participant_packet,
            participant_by_label=self.participants,
        )
        self._is_running = True

    async def stop(self) -> None:
        """Stops the forking session and cleans up all associated resources."""
        if not self._is_running:
            return
        await self._forking_session.stop()
        for stream in self._audio_streams.values():
            stream.stop()
        self._audio_streams.clear()
        self._is_running = False
