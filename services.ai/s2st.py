from dataclasses import dataclass
from uuid import uuid4

from cortexacommon.events.producer import EventProducer
from cortexacommon.events.schemas import TranscriptionEvent
from cortexacommon.logging import get_logger

from providers.base import BaseSTTProvider
from providers.factory import ProviderFactory
from config import settings

logger = get_logger(__name__)

@dataclass
class S2STProcessor:
    """
    Orchestrates the Speech-to-Speech Translation (S2ST) pipeline.
    This version receives complete utterances and focuses on business logic.
    """
    events_producer: EventProducer
    stt: BaseSTTProvider

    @classmethod
    async def create(cls, events_producer: EventProducer) -> "S2STProcessor":
        """Asynchronously creates and initializes the STT provider."""
        stt = ProviderFactory.create_stt_provider(
            settings.stt_provider,
            model_size=settings.whisper_model_size,
            compute_type=settings.whisper_compute_type,
            device=settings.whisper_device
        )
        await stt.initialize()
        logger.info("S2ST Processor (STT component) initialized successfully")
        return cls(events_producer=events_producer, stt=stt)

    async def handle_utterance(self, utterance_data: bytes, participant_id: str, call_id: str) -> None:
        """
        Processes a complete utterance provided by the UtteranceDetector.
        This method is the callback.
        """
        transcription_result = await self.stt.transcribe(utterance_data)
        if not transcription_result or not transcription_result[0].strip():
            logger.info(f"STT produced no text for {participant_id}.")
            return

        original_text, confidence = transcription_result
        logger.info(f"STT successful for {participant_id}: '{original_text}'")

        await self.events_producer.publish_event(
            "transcription.completed",
            TranscriptionEvent(
                segment_id=str(uuid4()),
                call_id=call_id,
                participant_id=participant_id,
                text=original_text,
                confidence=confidence,
            )
        )