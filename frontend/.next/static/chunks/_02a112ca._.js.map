{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;;AAII;AAJJ;AAAA;;AAEO,SAAS;IACd,OAAO,IAAA,oMAAmB;AAI5B", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,0KAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,2KAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,4KAAmB;QAClB,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/components/login-form.tsx"], "sourcesContent": ["'use client'\n\nimport { cn } from '@/lib/utils'\nimport { createClient } from '@/lib/supabase/client'\nimport { Button } from '@/components/ui/button'\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useState } from 'react'\n\nexport function LoginForm({ className, ...props }: React.ComponentPropsWithoutRef<'div'>) {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [error, setError] = useState<string | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const router = useRouter()\n\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault()\n    const supabase = createClient()\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n      if (error) throw error\n      // Update this route to redirect to an authenticated route. The user already has an active session.\n      router.push('/')\n    } catch (error: unknown) {\n      setError(error instanceof Error ? error.message : 'An error occurred')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className={cn('flex flex-col gap-6', className)} {...props}>\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-2xl\">Login</CardTitle>\n          <CardDescription>Enter your email below to login to your account</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleLogin}>\n            <div className=\"flex flex-col gap-6\">\n              <div className=\"grid gap-2\">\n                <Label htmlFor=\"email\">Email</Label>\n                <Input\n                  id=\"email\"\n                  type=\"email\"\n                  placeholder=\"<EMAIL>\"\n                  required\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                />\n              </div>\n              <div className=\"grid gap-2\">\n                <div className=\"flex items-center\">\n                  <Label htmlFor=\"password\">Password</Label>\n                  <Link\n                    href=\"/auth/forgot-password\"\n                    className=\"ml-auto inline-block text-sm underline-offset-4 hover:underline\"\n                  >\n                    Forgot your password?\n                  </Link>\n                </div>\n                <Input\n                  id=\"password\"\n                  type=\"password\"\n                  required\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                />\n              </div>\n              {error && <p className=\"text-sm text-red-500\">{error}</p>}\n              <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\n                {isLoading ? 'Logging in...' : 'Login'}\n              </Button>\n            </div>\n            <div className=\"mt-4 text-center text-sm\">\n              Don&apos;t have an account?{' '}\n              <Link href=\"/auth/sign-up\" className=\"underline underline-offset-4\">\n                Sign up\n              </Link>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;;;AAhBA;;;;;;;;;;AAkBO,SAAS,UAAU,KAA8D;QAA9D,EAAE,SAAS,EAAE,GAAG,OAA8C,GAA9D;;IACxB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAC;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAC;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAgB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAC3C,MAAM,SAAS,IAAA,kJAAS;IAExB,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAChB,MAAM,WAAW,IAAA,4IAAY;QAC7B,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;gBACvD;gBACA;YACF;YACA,IAAI,OAAO,MAAM;YACjB,mGAAmG;YACnG,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAgB;YACvB,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,IAAA,qHAAE,EAAC,uBAAuB;QAAa,GAAG,KAAK;kBAC7D,cAAA,6LAAC,oIAAI;;8BACH,6LAAC,0IAAU;;sCACT,6LAAC,yIAAS;4BAAC,WAAU;sCAAW;;;;;;sCAChC,6LAAC,+IAAe;sCAAC;;;;;;;;;;;;8BAEnB,6LAAC,2IAAW;8BACV,cAAA,6LAAC;wBAAK,UAAU;;0CACd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,sIAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,6LAAC,sIAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,aAAY;gDACZ,QAAQ;gDACR,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAG5C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,sIAAK;wDAAC,SAAQ;kEAAW;;;;;;kEAC1B,6LAAC,0KAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;0DAIH,6LAAC,sIAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,QAAQ;gDACR,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;oCAG9C,uBAAS,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDAC/C,6LAAC,wIAAM;wCAAC,MAAK;wCAAS,WAAU;wCAAS,UAAU;kDAChD,YAAY,kBAAkB;;;;;;;;;;;;0CAGnC,6LAAC;gCAAI,WAAU;;oCAA2B;oCACZ;kDAC5B,6LAAC,0KAAI;wCAAC,MAAK;wCAAgB,WAAU;kDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlF;GAnFgB;;QAKC,kJAAS;;;KALV", "debugId": null}}]}