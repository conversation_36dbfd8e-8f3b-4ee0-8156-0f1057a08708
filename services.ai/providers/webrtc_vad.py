from webrtcvad import Vad

from cortexacommon.logging import get_logger

from .base import BaseVADProvider

logger = get_logger(__name__)


class WebRTCVADProvider(BaseVADProvider):
    """Voice Activity Detection provider using WebRTC VAD."""

    def __init__(
        self,
        aggressiveness: int = 3,
        frame_duration_ms: int = 30,
        sample_rate: int = 16000,
        **kwargs,
    ):
        """
        Initialize WebRTC VAD provider.

        Args:
            aggressiveness: VAD aggressiveness level (0-3)
            frame_duration_ms: Frame duration in milliseconds (10, 20, or 30)
            sample_rate: PCM sample rate in Hz (8k, 16k, 32k, or 48k)
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        self.aggressiveness = aggressiveness
        self.frame_duration_ms = frame_duration_ms
        self.sample_rate = sample_rate
        # Number of samples per frame
        self._frame_size = int(self.sample_rate * self.frame_duration_ms / 1000)
        # Bytes per frame for 16-bit PCM mono
        self._frame_size_bytes = self._frame_size * 2
        self._vad: Vad | None = None

    async def initialize(self) -> None:
        """Initialize WebRTC VAD."""
        self._vad = Vad(self.aggressiveness)
        self._initialized = True
        logger.info(
            f"WebRTC VAD initialized (agg={self.aggressiveness}, frame={self.frame_duration_ms}ms, sr={self.sample_rate})",
        )

    def is_speech(self, audio_frame: bytes) -> bool:
        """
        Detect if the buffer contains speech. Accepts any length; processes
        in contiguous windows of size frame_size.

        Args:
            audio_frame: PCM s16le mono bytes at self.sample_rate

        Returns:
            bool: True if any window contains speech
        """
        assert self._vad is not None, "WebRTC VAD not initialized"

        n = len(audio_frame)
        if n < self._frame_size_bytes:
            return False

        # Iterate over non-overlapping frames; if you want overlap, adjust step
        for start in range(0, n - self._frame_size_bytes + 1, self._frame_size_bytes):
            window = audio_frame[start : start + self._frame_size_bytes]
            try:
                if self._vad.is_speech(window, self.sample_rate):
                    return True
            except Exception as e:
                logger.warning(f"WebRTC VAD error on window: {e}")
                # Continue to next window
                continue
        return False

    @property
    def frame_size(self) -> int:
        """Get the required frame size in samples for VAD processing."""
        return self._frame_size
