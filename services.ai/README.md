# Cortexa AI Speech Service

Real-time voice translation service for the Cortexa platform. This service manages stateful, bidirectional WebSocket connections for live speech-to-speech translation.

## GPU Acceleration

This service supports GPU acceleration for the Whisper speech-to-text model, which can significantly improve transcription performance.

### Prerequisites

1. **NVIDIA GPU**: A CUDA-compatible NVIDIA GPU
2. **NVIDIA Docker Runtime**: Install the NVIDIA Container Toolkit
3. **Docker Compose GPU Support**: Docker Compose version 2.3+ with GPU support

### Installation

#### Install NVIDIA Container Toolkit (Ubuntu/Debian)

```bash
# Add NVIDIA package repositories
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

# Install nvidia-docker2
sudo apt-get update && sudo apt-get install -y nvidia-docker2

# Restart Docker daemon
sudo systemctl restart docker
```

#### Verify GPU Access

Test that Docker can access your GPU:

```bash
docker run --rm --gpus all nvidia/cuda:12.1-base-ubuntu22.04 nvidia-smi
```

### Configuration

The AI service is pre-configured for GPU acceleration with the following settings:

- `WHISPER_DEVICE=cuda`: Use GPU for Whisper model
- `WHISPER_COMPUTE_TYPE=float16`: Use half-precision for better GPU performance

### Testing GPU Setup

Run the GPU check script inside the container:

```bash
# Build and run the AI service
docker-compose up --build ai

# In another terminal, check GPU availability
docker-compose exec ai poetry run python gpu_check.py
```

### Performance Comparison

| Configuration | Device | Compute Type | Typical Performance |
|---------------|--------|--------------|-------------------|
| CPU (Default) | cpu    | int8         | ~2-5x real-time   |
| GPU (Optimal) | cuda   | float16      | ~10-20x real-time |

### Troubleshooting

#### GPU Not Detected

1. Verify NVIDIA drivers: `nvidia-smi`
2. Check Docker GPU access: `docker run --rm --gpus all nvidia/cuda:12.1-base-ubuntu22.04 nvidia-smi`
3. Ensure Docker Compose version supports GPU: `docker-compose --version`

#### Out of Memory Errors

If you encounter GPU memory issues, try:

1. Use a smaller Whisper model: `WHISPER_MODEL_SIZE=base.en` or `tiny.en`
2. Reduce compute precision: `WHISPER_COMPUTE_TYPE=int8`
3. Fall back to CPU: `WHISPER_DEVICE=cpu`

#### Fallback to CPU

The service automatically falls back to CPU if GPU is not available. Check logs for:
```
Whisper model initialized on cpu
```
