import asyncio

from cortexacommon.logging import get_logger
from cortexacommon.events.producer import EventProducer
from cortexacommon.clients.participant_presence import ParticipantPresenceClient

from config import settings
from core.call_queue_handler import CallQueueHandler


logger = get_logger(__name__)


class ApplicationContext:
    _instance: "ApplicationContext | None" = None
    _lock = asyncio.Lock()

    def __init__(self):
        self._producer: EventProducer | None = None
        self._participant_client: ParticipantPresenceClient | None = None
        self._queue_handler: CallQueueHandler | None = None

    @classmethod
    async def get_instance(cls) -> "ApplicationContext":
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    @property
    def event_producer(self) -> EventProducer:
        assert self._producer is not None, "Event producer not initialized"
        return self._producer

    @property
    def participant_client(self) -> ParticipantPresenceClient:
        assert self._participant_client is not None, "Participant client not initialized"
        return self._participant_client
    
    @property
    def queue_handler(self) -> CallQueueHandler:
        assert self._queue_handler is not None, "Queue handler not initialized"
        return self._queue_handler

    async def initialize(self) -> None:
        self._producer = EventProducer(settings.kafka_settings)
        await self._producer.start()
        self._participant_client = ParticipantPresenceClient(settings.participant_service_base_url)
        self._queue_handler = CallQueueHandler(
            self._participant_client,
            self._producer,
            max_attempts=settings.assignment_retry_attempts,
            interval_ms=settings.assignment_retry_interval_ms,
        )

    async def cleanup(self) -> None:
        if self._producer:
            await self._producer.stop()
            self._producer = None

