from typing import Protocol, runtime_checkable


@runtime_checkable
class BaseAudioPacketHandler(Protocol):
    """
    Defines the contract for any class that processes audio packets.

    The common SessionManager will pass decoded audio packets to a concrete
    implementation of this protocol. This allows service-specific logic
    (like S2ST, recording, etc.) to be plugged into the generic audio
    consumption pipeline.
    """

    async def handle_packet(
        self, pcm_data: bytes, participant_id: str, call_id: str
    ) -> None:
        """
        Process a chunk of decoded PCM audio data for a specific participant.

        Args:
            pcm_data: The raw PCM audio bytes (s16le, mono).
            participant_id: The unique identifier for the participant.
            call_id: The unique identifier for the call.
        """
        ...
