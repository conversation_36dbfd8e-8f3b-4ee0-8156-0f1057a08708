from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    model_config = SettingsConfigDict(
        env_prefix="DB_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    uri: str = Field(default="postgresql+psycopg://user:password@localhost:5432/cortexa", description="Database URI")


class KafkaSettings(BaseSettings):
    """Kafka configuration settings."""
    
    model_config = SettingsConfigDict(
        env_prefix="KAFKA_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    bootstrap_servers: str = Field(default="localhost:9092", description="Kafka bootstrap servers")
    group_id: str = Field(default="cortexa-group", description="Kafka consumer group ID")


class SecuritySettings(BaseSettings):
    """Security configuration settings."""

    model_config = SettingsConfigDict(
        env_prefix="SUPABASE_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    jwt_secret: str = Field(default="", description="Supabase JWT secret")


class SessionAudioSettings(BaseSettings):
    """Audio streaming settings for session forked audio"""

    model_config = SettingsConfigDict(
        env_prefix="AUDIO_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    sample_rate: int = Field(
        default=16000,
        description="Audio sample rate in Hz"
    )
