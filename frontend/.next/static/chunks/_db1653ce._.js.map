{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/lib/services/base.ts"], "sourcesContent": ["\"use client\"\n\nimport type { SupabaseClient } from '@supabase/supabase-js'\nimport { createClient as createBrowserSupabase } from '@/lib/supabase/client'\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type EventListener = (...args: any[]) => void;\n\nexport class EventEmitter {\n  private listeners: Record<string, EventListener[]> = {};\n\n  on(event: string, listener: EventListener) {\n    if (!this.listeners[event]) this.listeners[event] = [];\n    this.listeners[event].push(listener);\n  }\n\n  off(event: string, listener: EventListener) {\n    if (!this.listeners[event]) return;\n    this.listeners[event] = this.listeners[event].filter(l => l !== listener);\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  emit(event: string, ...args: any[]) {\n    if (!this.listeners[event]) return;\n    this.listeners[event].forEach(listener => listener(...args));\n  }\n}\n\nexport abstract class BaseService extends EventEmitter {\n  protected _supabase: SupabaseClient\n  protected _apiBaseUrl: string\n\n  constructor(supabaseClient: SupabaseClient, apiBaseUrl: string) {\n    super();\n    this._supabase = supabaseClient\n    this._apiBaseUrl = apiBaseUrl\n  }\n\n  protected async getAccessToken(): Promise<string> {\n    const { data, error } = await this._supabase.auth.getSession()\n    if (error || !data.session?.access_token) {\n      throw new Error('Not authenticated or missing access token')\n    }\n    return data.session.access_token\n  }\n\n  protected getWsUrl(endpoint: string): string {\n    return `${this._apiBaseUrl.replace(/^http/, 'ws')}${endpoint}`\n  }\n}\n\nexport abstract class BaseWebSocketService extends BaseService {\n  protected _ws: WebSocket | null = null\n\n  protected abstract _setupWsEvents(): void\n\n  protected async _connectTo(endpoint: string): Promise<void> {\n    if (this._ws?.readyState === WebSocket.OPEN) {\n      console.log('WebSocket already connected.')\n      return\n    }\n    try {\n      const token = await this.getAccessToken()\n      const wsUrl = `${this.getWsUrl(endpoint)}?token=${token}`\n      this._ws = new WebSocket(wsUrl)\n      this._setupWsEvents()\n    } catch (error) {\n      console.error('Failed to establish websocket connection:', error)\n      this.emit('error', error)\n    }\n  }\n\n  disconnect(): void {\n    if (this._ws) {\n      this._ws.close(1000, 'User initiated disconnect')\n    }\n  }\n}\n\nexport class BaseHttpClient extends BaseService {\n  protected async authorizedFetch(input: RequestInfo | URL, init: RequestInit = {}): Promise<Response> {\n    const token = await this.getAccessToken()\n    const headers = new Headers(init.headers || {})\n    headers.set('Authorization', `Bearer ${token}`)\n\n    // Set default Content-Type for POST, PUT, etc. if not already set\n    if (!headers.has('Content-Type') && init.method && init.method !== 'GET') {\n      headers.set('Content-Type', 'application/json')\n    }\n\n    const response = await fetch(input, { ...init, headers, credentials: 'include' })\n\n    if (!response.ok) {\n      const errorText = await response.text().catch(() => 'Could not read error response.')\n      const method = init.method || 'GET'\n      throw new Error(`API Error: ${method} ${input} failed with status ${response.status}: ${errorText}`)\n    }\n\n    return response\n  }\n}\n\nexport const defaultSupabase = createBrowserSupabase()\nexport function apiBaseUrlFromEnv(): string {\n  const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL\n  if (!apiBaseUrl) {\n    throw new Error('Missing NEXT_PUBLIC_API_BASE_URL environment variable.')\n  }\n  return apiBaseUrl\n}\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwGqB;;AArGrB;AAHA;;;AAQO,MAAM;IAGX,GAAG,KAAa,EAAE,QAAuB,EAAE;QACzC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE;QACtD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;IAC7B;IAEA,IAAI,KAAa,EAAE,QAAuB,EAAE;QAC1C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;QAC5B,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;IAClE;IAEA,8DAA8D;IAC9D,KAAK,KAAa,EAAkB;QAAhB,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,OAAH,UAAA,OAAA,IAAA,OAAA,QAAA,OAAA,GAAA,OAAA,MAAA;YAAG,KAAH,OAAA,KAAA,SAAA,CAAA,KAAc;;QAChC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;QAC5B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA,WAAY,YAAY;IACxD;;QAhBA,+KAAQ,aAA6C,CAAC;;AAiBxD;AAEO,MAAe,oBAAoB;IAUxC,MAAgB,iBAAkC;YAElC;QADd,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU;QAC5D,IAAI,SAAS,GAAC,gBAAA,KAAK,OAAO,cAAZ,oCAAA,cAAc,YAAY,GAAE;YACxC,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,KAAK,OAAO,CAAC,YAAY;IAClC;IAEU,SAAS,QAAgB,EAAU;QAC3C,OAAO,AAAC,GAA4C,OAA1C,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,OAAiB,OAAT;IACtD;IAhBA,YAAY,cAA8B,EAAE,UAAkB,CAAE;QAC9D,KAAK,IAJP,+KAAU,aAAV,KAAA,IACA,+KAAU,eAAV,KAAA;QAIE,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;IACrB;AAaF;AAEO,MAAe,6BAA6B;IAKjD,MAAgB,WAAW,QAAgB,EAAiB;YACtD;QAAJ,IAAI,EAAA,YAAA,IAAI,CAAC,GAAG,cAAR,gCAAA,UAAU,UAAU,MAAK,UAAU,IAAI,EAAE;YAC3C,QAAQ,GAAG,CAAC;YACZ;QACF;QACA,IAAI;YACF,MAAM,QAAQ,MAAM,IAAI,CAAC,cAAc;YACvC,MAAM,QAAQ,AAAC,GAAmC,OAAjC,IAAI,CAAC,QAAQ,CAAC,WAAU,WAAe,OAAN;YAClD,IAAI,CAAC,GAAG,GAAG,IAAI,UAAU;YACzB,IAAI,CAAC,cAAc;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,IAAI,CAAC,IAAI,CAAC,SAAS;QACrB;IACF;IAEA,aAAmB;QACjB,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM;QACvB;IACF;;QAzBK,gBACL,+KAAU,OAAwB;;AAyBpC;AAEO,MAAM,uBAAuB;IAClC,MAAgB,gBAAgB,KAAwB,EAA6C;YAA3C,OAAA,iEAAoB,CAAC;QAC7E,MAAM,QAAQ,MAAM,IAAI,CAAC,cAAc;QACvC,MAAM,UAAU,IAAI,QAAQ,KAAK,OAAO,IAAI,CAAC;QAC7C,QAAQ,GAAG,CAAC,iBAAiB,AAAC,UAAe,OAAN;QAEvC,kEAAkE;QAClE,IAAI,CAAC,QAAQ,GAAG,CAAC,mBAAmB,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK,OAAO;YACxE,QAAQ,GAAG,CAAC,gBAAgB;QAC9B;QAEA,MAAM,WAAW,MAAM,MAAM,OAAO;YAAE,GAAG,IAAI;YAAE;YAAS,aAAa;QAAU;QAE/E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM;YACpD,MAAM,SAAS,KAAK,MAAM,IAAI;YAC9B,MAAM,IAAI,MAAM,AAAC,cAAuB,OAAV,QAAO,KAA+B,OAA5B,OAAM,wBAA0C,OAApB,SAAS,MAAM,EAAC,MAAc,OAAV;QAC1F;QAEA,OAAO;IACT;AACF;AAEO,MAAM,kBAAkB,IAAA,4IAAqB;AAC7C,SAAS;IACd,MAAM;IACN;;IAGA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/lib/services/user.ts"], "sourcesContent": ["\"use client\"\n\nimport { BaseHttpClient, defaultSupabase, apiBaseUrlFromEnv } from './base'\nimport type { SupabaseClient } from '@supabase/supabase-js'\n\nexport const ROLES = ['operator', 'translator', 'administrator', 'manager'] as const\nexport type Role = (typeof ROLES)[number]\n\nexport type UserCreate = {\n  email: string\n  password: string\n  role: Role\n}\n\nexport type UserResponse = {\n  id: string\n  role: string | null\n  email: string\n  created_at: string\n}\n\nexport class UserApiClient extends BaseHttpClient {\n  #baseUrl: string\n\n  /**\n   * Initializes the User API Client.\n   * @param supabaseClient - An instance of the Supabase client.\n   * @param apiBaseUrl - The base URL for the API endpoint.\n   */\n  constructor(supabaseClient: SupabaseClient, apiBaseUrl: string) {\n    super(supabaseClient, apiBaseUrl)\n    this.#baseUrl = `${apiBaseUrl}/users`\n  }\n\n  // Auth and authorizedFetch provided by BaseHttpClient\n\n  /**\n   * Fetches a list of all users.\n   * Corresponds to: GET /users\n   * @returns A promise that resolves with an array of user objects.\n   */\n  async listUsers(): Promise<UserResponse[]> {\n    const res = await this.authorizedFetch(this.#baseUrl, { method: 'GET' })\n    return res.json()\n  }\n\n  /**\n   * Creates a new user.\n   * Corresponds to: POST /users\n   * @param payload - The data for the new user.\n   * @returns A promise that resolves with the created user object.\n   */\n  async createUser(payload: UserCreate): Promise<UserResponse> {\n    const res = await this.authorizedFetch(this.#baseUrl, {\n      method: 'POST',\n      body: JSON.stringify(payload),\n    })\n    return res.json()\n  }\n\n  /**\n   * Assigns a role to a specific user.\n   * Corresponds to: PUT /users/{user_id}/roles/{role_name}\n   * @param userId - The ID of the user.\n   * @param roleName - The role to assign.\n   * @returns A promise that resolves when the operation is complete.\n   */\n  async assignRole(userId: string, roleName: Role): Promise<void> {\n    const url = `${this.#baseUrl}/${encodeURIComponent(userId)}/roles/${encodeURIComponent(roleName)}`\n    await this.authorizedFetch(url, { method: 'PUT' })\n  }\n\n  /**\n   * Deletes a specific user.\n   * Corresponds to: DELETE /users/{user_id}\n   * @param userId - The ID of the user to delete.\n   * @returns A promise that resolves when the operation is complete.\n   */\n  async deleteUser(userId: string): Promise<void> {\n    const url = `${this.#baseUrl}/${encodeURIComponent(userId)}`\n    await this.authorizedFetch(url, { method: 'DELETE' })\n  }\n}\n\n// --- Singleton Instance ---\nconst supabase = defaultSupabase\nconst apiBaseUrl = apiBaseUrlFromEnv()\n\nexport const userManagementApi = new UserApiClient(supabase, apiBaseUrl);"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAFA;;;;;AAKO,MAAM,QAAQ;IAAC;IAAY;IAAc;IAAiB;CAAU;IAiBzE;AADK,MAAM,sBAAsB,4IAAc;IAa/C,sDAAsD;IAEtD;;;;GAIC,GACD,MAAM,YAAqC;QACzC,MAAM,MAAM,MAAM,IAAI,CAAC,eAAe,kLAAC,IAAI,EAAC,WAAU;YAAE,QAAQ;QAAM;QACtE,OAAO,IAAI,IAAI;IACjB;IAEA;;;;;GAKC,GACD,MAAM,WAAW,OAAmB,EAAyB;QAC3D,MAAM,MAAM,MAAM,IAAI,CAAC,eAAe,kLAAC,IAAI,EAAC,WAAU;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,IAAI,IAAI;IACjB;IAEA;;;;;;GAMC,GACD,MAAM,WAAW,MAAc,EAAE,QAAc,EAAiB;QAC9D,MAAM,MAAM,AAAC,GAAmB,wLAAjB,IAAI,EAAC,WAAS,KAAuC,OAApC,mBAAmB,SAAQ,WAAsC,OAA7B,mBAAmB;QACvF,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK;YAAE,QAAQ;QAAM;IAClD;IAEA;;;;;GAKC,GACD,MAAM,WAAW,MAAc,EAAiB;QAC9C,MAAM,MAAM,AAAC,GAAmB,wLAAjB,IAAI,EAAC,WAAS,KAA8B,OAA3B,mBAAmB;QACnD,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK;YAAE,QAAQ;QAAS;IACrD;IAzDA;;;;GAIC,GACD,YAAY,cAA8B,EAAE,UAAkB,CAAE;QAC9D,KAAK,CAAC,gBAAgB,aARxB,wLAAA;;mBAAA,KAAA;;+LASO,UAAW,AAAC,GAAa,OAAX,YAAW;IAChC;AAkDF;AAEA,6BAA6B;AAC7B,MAAM,WAAW,6IAAe;AAChC,MAAM,aAAa,IAAA,+IAAiB;AAE7B,MAAM,oBAAoB,IAAI,cAAc,UAAU", "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,qHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,4KAAmB;QAClB,aAAU;QACV,WAAW,IAAA,qHAAE,EACX,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/components/Admin/UserManagementPanel.tsx"], "sourcesContent": ["'use client'\n\nimport { FormEvent, useEffect, useMemo, useState } from 'react'\nimport { userManagementApi, ROL<PERSON>, Role, UserResponse } from '@/lib/services/user'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\n\nexport function UserManagementPanel() {\n  // State for the list of users and loading/error status\n  const [users, setUsers] = useState<UserResponse[]>([])\n  const [loading, setLoading] = useState<boolean>(true)\n  const [error, setError] = useState<string | null>(null)\n\n  // State for the \"Create User\" form\n  const [email, setEmail] = useState<string>('')\n  const [password, setPassword] = useState<string>('')\n  const [role, setRole] = useState<Role>(ROLES[0]) // Default to the first role\n  const [creating, setCreating] = useState<boolean>(false)\n\n  /**\n   * Fetches the list of users from the API and updates the component's state.\n   */\n  const fetchUsers = async () => {\n    try {\n      setLoading(true)\n      setError(null)\n      const userList = await userManagementApi.listUsers()\n      setUsers(userList)\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // Fetch users when the component mounts\n  useEffect(() => {\n    fetchUsers()\n  }, [])\n\n  /**\n   * Memoized sorted list of users to prevent re-sorting on every render.\n   * Users are sorted by creation date in descending order (newest first).\n   */\n  const sortedUsers = useMemo(() => {\n    return [...users].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())\n  }, [users])\n\n  /**\n   * Handles the form submission for creating a new user.\n   */\n  const handleCreateUser = async (e: FormEvent) => {\n    e.preventDefault()\n    if (!email || !password) return\n\n    setCreating(true)\n    setError(null)\n    try {\n      await userManagementApi.createUser({ email, password, role })\n      // Reset form and refresh user list\n      setEmail('')\n      setPassword('')\n      setRole(ROLES[0])\n      await fetchUsers()\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to create user')\n    } finally {\n      setCreating(false)\n    }\n  }\n\n  /**\n   * Handles assigning a new role to a user.\n   */\n  const handleAssignRole = async (userId: string, newRole: Role) => {\n    // Optimistically update the UI\n    setUsers(users.map(u => u.id === userId ? { ...u, role: newRole } : u))\n\n    try {\n      await userManagementApi.assignRole(userId, newRole)\n      // Optionally, you could re-fetch here to confirm, but optimistic is faster\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to assign role')\n      // Revert the change on error\n      fetchUsers()\n    }\n  }\n\n  /**\n   * Handles the deletion of a user.\n   */\n  const handleDeleteUser = async (userId: string) => {\n    if (!window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {\n        return;\n    }\n    \n    // Optimistically remove from UI\n    setUsers(users.filter((u) => u.id !== userId))\n\n    try {\n      await userManagementApi.deleteUser(userId)\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to delete user')\n      // Revert the change on error by re-fetching\n      fetchUsers()\n    }\n  }\n\n  return (\n    <div className=\"mx-auto max-w-5xl w-full space-y-6 p-4\">\n      <Card>\n        <CardHeader>\n          <CardTitle>User Management</CardTitle>\n        </CardHeader>\n        <CardContent>\n          {loading ? (\n            <div className=\"text-center py-4\">Loading users...</div>\n          ) : error ? (\n            <div className=\"text-red-600 bg-red-50 p-4 rounded-md\">{error}</div>\n          ) : (\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full text-sm\">\n                <thead>\n                  <tr className=\"text-left border-b\">\n                    <th className=\"py-2 pr-4 font-semibold\">Email</th>\n                    <th className=\"py-2 pr-4 font-semibold\">Role</th>\n                    <th className=\"py-2 pr-4 font-semibold\">Created</th>\n                    <th className=\"py-2 font-semibold text-right\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {sortedUsers.map((u) => (\n                    <tr key={u.id} className=\"border-b hover:bg-gray-50\">\n                      <td className=\"py-2 pr-4 font-medium\">{u.email}</td>\n                      <td className=\"py-2 pr-4\">\n                        <select\n                          className=\"border rounded px-2 py-1 bg-white\"\n                          value={u.role ?? ''}\n                          onChange={(e) => handleAssignRole(u.id, e.target.value as Role)}\n                        >\n                          <option value=\"\">(none)</option>\n                          {ROLES.map((r) => (\n                            <option key={r} value={r}>\n                              {r.charAt(0).toUpperCase() + r.slice(1)}\n                            </option>\n                          ))}\n                        </select>\n                      </td>\n                      <td className=\"py-2 pr-4 text-gray-600\">\n                        {new Date(u.created_at).toLocaleString()}\n                      </td>\n                      <td className=\"py-2\">\n                        <div className=\"flex gap-2 justify-end\">\n                          <Button\n                            variant=\"destructive\"\n                            size=\"sm\"\n                            onClick={() => handleDeleteUser(u.id)}\n                            title=\"Delete user\"\n                          >\n                            Delete\n                          </Button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardHeader>\n          <CardTitle>Create New User</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <form className=\"grid grid-cols-1 md:grid-cols-4 gap-4 items-end\" onSubmit={handleCreateUser}>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">Email</Label>\n              <Input id=\"email\" value={email} onChange={(e) => setEmail(e.target.value)} required type=\"email\" placeholder=\"<EMAIL>\" />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"password\">Password</Label>\n              <Input id=\"password\" value={password} onChange={(e) => setPassword(e.target.value)} required type=\"password\" placeholder=\"••••••••\" />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"role\">Role</Label>\n              <select\n                id=\"role\"\n                className=\"w-full border rounded px-2 py-2 h-10 bg-white\"\n                value={role}\n                onChange={(e) => setRole(e.target.value as Role)}\n              >\n                {ROLES.map((r) => (\n                  <option key={r} value={r}>\n                    {r.charAt(0).toUpperCase() + r.slice(1)}\n                  </option>\n                ))}\n              </select>\n            </div>\n            <div className=\"md:pt-2\">\n              <Button type=\"submit\" disabled={creating} className=\"w-full\">\n                {creating ? 'Creating...' : 'Create User'}\n              </Button>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASO,SAAS;;IACd,uDAAuD;IACvD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAiB,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAU;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAgB;IAElD,mCAAmC;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAS;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAS;IACjD,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAAO,mIAAK,CAAC,EAAE,EAAE,4BAA4B;;IAC7E,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAU;IAElD;;GAEC,GACD,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,+IAAiB,CAAC,SAAS;YAClD,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,wCAAwC;IACxC,IAAA,0KAAS;yCAAC;YACR;QACF;wCAAG,EAAE;IAEL;;;GAGC,GACD,MAAM,cAAc,IAAA,wKAAO;oDAAC;YAC1B,OAAO;mBAAI;aAAM,CAAC,IAAI;4DAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;;QACpG;mDAAG;QAAC;KAAM;IAEV;;GAEC,GACD,MAAM,mBAAmB,OAAO;QAC9B,EAAE,cAAc;QAChB,IAAI,CAAC,SAAS,CAAC,UAAU;QAEzB,YAAY;QACZ,SAAS;QACT,IAAI;YACF,MAAM,+IAAiB,CAAC,UAAU,CAAC;gBAAE;gBAAO;gBAAU;YAAK;YAC3D,mCAAmC;YACnC,SAAS;YACT,YAAY;YACZ,QAAQ,mIAAK,CAAC,EAAE;YAChB,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,YAAY;QACd;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,OAAO,QAAgB;QAC9C,+BAA+B;QAC/B,SAAS,MAAM,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;gBAAE,GAAG,CAAC;gBAAE,MAAM;YAAQ,IAAI;QAEpE,IAAI;YACF,MAAM,+IAAiB,CAAC,UAAU,CAAC,QAAQ;QAC3C,2EAA2E;QAC7E,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,6BAA6B;YAC7B;QACF;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,OAAO,OAAO,CAAC,6EAA6E;YAC7F;QACJ;QAEA,gCAAgC;QAChC,SAAS,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;QAEtC,IAAI;YACF,MAAM,+IAAiB,CAAC,UAAU,CAAC;QACrC,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,4CAA4C;YAC5C;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,oIAAI;;kCACH,6LAAC,0IAAU;kCACT,cAAA,6LAAC,yIAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,2IAAW;kCACT,wBACC,6LAAC;4BAAI,WAAU;sCAAmB;;;;;mCAChC,sBACF,6LAAC;4BAAI,WAAU;sCAAyC;;;;;iDAExD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;kDACC,cAAA,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,6LAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,6LAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,6LAAC;oDAAG,WAAU;8DAAgC;;;;;;;;;;;;;;;;;kDAGlD,6LAAC;kDACE,YAAY,GAAG,CAAC,CAAC;gDAMH;iEALb,6LAAC;gDAAc,WAAU;;kEACvB,6LAAC;wDAAG,WAAU;kEAAyB,EAAE,KAAK;;;;;;kEAC9C,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DACC,WAAU;4DACV,OAAO,CAAA,UAAA,EAAE,IAAI,cAAN,qBAAA,UAAU;4DACjB,UAAU,CAAC,IAAM,iBAAiB,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;;8EAEtD,6LAAC;oEAAO,OAAM;8EAAG;;;;;;gEAChB,mIAAK,CAAC,GAAG,CAAC,CAAC,kBACV,6LAAC;wEAAe,OAAO;kFACpB,EAAE,MAAM,CAAC,GAAG,WAAW,KAAK,EAAE,KAAK,CAAC;uEAD1B;;;;;;;;;;;;;;;;kEAMnB,6LAAC;wDAAG,WAAU;kEACX,IAAI,KAAK,EAAE,UAAU,EAAE,cAAc;;;;;;kEAExC,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,wIAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,iBAAiB,EAAE,EAAE;gEACpC,OAAM;0EACP;;;;;;;;;;;;;;;;;+CA1BE,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAwC3B,6LAAC,oIAAI;;kCACH,6LAAC,0IAAU;kCACT,cAAA,6LAAC,yIAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,2IAAW;kCACV,cAAA,6LAAC;4BAAK,WAAU;4BAAkD,UAAU;;8CAC1E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,sIAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,sIAAK;4CAAC,IAAG;4CAAQ,OAAO;4CAAO,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CAAG,QAAQ;4CAAC,MAAK;4CAAQ,aAAY;;;;;;;;;;;;8CAE/G,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,sIAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC,sIAAK;4CAAC,IAAG;4CAAW,OAAO;4CAAU,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAAG,QAAQ;4CAAC,MAAK;4CAAW,aAAY;;;;;;;;;;;;8CAE3H,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,sIAAK;4CAAC,SAAQ;sDAAO;;;;;;sDACtB,6LAAC;4CACC,IAAG;4CACH,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;sDAEtC,mIAAK,CAAC,GAAG,CAAC,CAAC,kBACV,6LAAC;oDAAe,OAAO;8DACpB,EAAE,MAAM,CAAC,GAAG,WAAW,KAAK,EAAE,KAAK,CAAC;mDAD1B;;;;;;;;;;;;;;;;8CAMnB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,wIAAM;wCAAC,MAAK;wCAAS,UAAU;wCAAU,WAAU;kDACjD,WAAW,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C;GA5MgB;KAAA", "debugId": null}}, {"offset": {"line": 901, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/node_modules/%40swc/helpers/esm/_class_apply_descriptor_get.js"], "sourcesContent": ["function _class_apply_descriptor_get(receiver, descriptor) {\n    if (descriptor.get) return descriptor.get.call(receiver);\n\n    return descriptor.value;\n}\nexport { _class_apply_descriptor_get as _ };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,4BAA4B,QAAQ,EAAE,UAAU;IACrD,IAAI,WAAW,GAAG,EAAE,OAAO,WAAW,GAAG,CAAC,IAAI,CAAC;IAE/C,OAAO,WAAW,KAAK;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/node_modules/%40swc/helpers/esm/_class_extract_field_descriptor.js"], "sourcesContent": ["function _class_extract_field_descriptor(receiver, privateMap, action) {\n    if (!privateMap.has(receiver)) throw new TypeError(\"attempted to \" + action + \" private field on non-instance\");\n\n    return privateMap.get(receiver);\n}\nexport { _class_extract_field_descriptor as _ };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,gCAAgC,QAAQ,EAAE,UAAU,EAAE,MAAM;IACjE,IAAI,CAAC,WAAW,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU,kBAAkB,SAAS;IAE9E,OAAO,WAAW,GAAG,CAAC;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 927, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/node_modules/%40swc/helpers/esm/_class_private_field_get.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_get } from \"./_class_apply_descriptor_get.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_get(receiver, privateMap) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"get\");\n    return _class_apply_descriptor_get(receiver, descriptor);\n}\nexport { _class_private_field_get as _ };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,SAAS,yBAAyB,QAAQ,EAAE,UAAU;IAClD,IAAI,aAAa,IAAA,kLAA+B,EAAC,UAAU,YAAY;IACvE,OAAO,IAAA,8KAA2B,EAAC,UAAU;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/node_modules/%40swc/helpers/esm/_check_private_redeclaration.js"], "sourcesContent": ["function _check_private_redeclaration(obj, privateCollection) {\n    if (privateCollection.has(obj)) {\n        throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n    }\n}\nexport { _check_private_redeclaration as _ };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,6BAA6B,GAAG,EAAE,iBAAiB;IACxD,IAAI,kBAAkB,GAAG,CAAC,MAAM;QAC5B,MAAM,IAAI,UAAU;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/node_modules/%40swc/helpers/esm/_class_private_field_init.js"], "sourcesContent": ["import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_field_init(obj, privateMap, value) {\n    _check_private_redeclaration(obj, privateMap);\n    privateMap.set(obj, value);\n}\nexport { _class_private_field_init as _ };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,SAAS,0BAA0B,GAAG,EAAE,UAAU,EAAE,KAAK;IACrD,IAAA,+KAA4B,EAAC,KAAK;IAClC,WAAW,GAAG,CAAC,KAAK;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 973, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/node_modules/%40swc/helpers/esm/_class_apply_descriptor_set.js"], "sourcesContent": ["function _class_apply_descriptor_set(receiver, descriptor, value) {\n    if (descriptor.set) descriptor.set.call(receiver, value);\n    else {\n        if (!descriptor.writable) {\n            // This should only throw in strict mode, but class bodies are\n            // always strict and private fields can only be used inside\n            // class bodies.\n            throw new TypeError(\"attempted to set read only private field\");\n        }\n        descriptor.value = value;\n    }\n}\nexport { _class_apply_descriptor_set as _ };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,4BAA4B,QAAQ,EAAE,UAAU,EAAE,KAAK;IAC5D,IAAI,WAAW,GAAG,EAAE,WAAW,GAAG,CAAC,IAAI,CAAC,UAAU;SAC7C;QACD,IAAI,CAAC,WAAW,QAAQ,EAAE;YACtB,8DAA8D;YAC9D,2DAA2D;YAC3D,gBAAgB;YAChB,MAAM,IAAI,UAAU;QACxB;QACA,WAAW,KAAK,GAAG;IACvB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/node_modules/%40swc/helpers/esm/_class_private_field_set.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_set } from \"./_class_apply_descriptor_set.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_set(receiver, privateMap, value) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"set\");\n    _class_apply_descriptor_set(receiver, descriptor, value);\n    return value;\n}\nexport { _class_private_field_set as _ };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,SAAS,yBAAyB,QAAQ,EAAE,UAAU,EAAE,KAAK;IACzD,IAAI,aAAa,IAAA,kLAA+B,EAAC,UAAU,YAAY;IACvE,IAAA,8KAA2B,EAAC,UAAU,YAAY;IAClD,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1012, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/node_modules/%40swc/helpers/esm/_define_property.js"], "sourcesContent": ["function _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });\n    } else obj[key] = value;\n\n    return obj;\n}\nexport { _define_property as _ };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,iBAAiB,GAAG,EAAE,GAAG,EAAE,KAAK;IACrC,IAAI,OAAO,KAAK;QACZ,OAAO,cAAc,CAAC,KAAK,KAAK;YAAE,OAAO;YAAO,YAAY;YAAM,cAAc;YAAM,UAAU;QAAK;IACzG,OAAO,GAAG,CAAC,IAAI,GAAG;IAElB,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1032, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/node_modules/%40radix-ui/react-primitive/src/primitive.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createSlot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'select',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\ntype Primitives = { [E in (typeof NODES)[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    if (typeof window !== 'undefined') {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not necessary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { PrimitivePropsWithRef };\n"], "names": [], "mappings": ";;;;;;;;;AAAA,YAAY,WAAW;AACvB,YAAY,cAAc;AAC1B,SAAS,kBAAkB;AA4ChB;;;;;AA1CX,IAAM,QAAQ;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAcA,IAAM,YAAY,MAAM,MAAA,CAAO,CAAC,WAAW,SAAS;IAClD,MAAM,WAAO,iLAAA,EAAW,aAAiB,CAAE,MAAN,IAAI;IACzC,MAAM,OAAa,2KAAA,CAAW,CAAC,OAA2C,iBAAsB;QAC9F,MAAM,EAAE,OAAA,EAAS,GAAG,eAAe,CAAA,GAAI;QACvC,MAAM,OAAY,UAAU,OAAO;QAEnC,IAAI,OAAO,WAAW,aAAa;YAChC,MAAA,CAAe,OAAO,GAAA,CAAI,UAAU,CAAC,CAAA,GAAI;QAC5C;QAEA,OAAO,aAAA,GAAA,IAAA,6KAAA,EAAC,MAAA;YAAM,GAAG,cAAA;YAAgB,KAAK;QAAA,CAAc;IACtD,CAAC;IAED,KAAK,WAAA,GAAc,aAAiB,OAAJ,IAAI;IAEpC,OAAO;QAAE,GAAG,SAAA;QAAW,CAAC,IAAI,CAAA,EAAG;IAAK;AACtC,GAAG,CAAC,CAAe;AA2CnB,SAAS,4BAAmD,MAAA,EAAqB,KAAA,EAAU;IACzF,IAAI,OAAQ,CAAS,iLAAA,CAAU,IAAM,OAAO,aAAA,CAAc,KAAK,CAAC;AAClE;AAIA,IAAM,OAAO", "debugId": null}}, {"offset": {"line": 1097, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Source/cortexa/frontend/node_modules/%40radix-ui/react-label/src/label.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ComponentRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n"], "names": [], "mappings": ";;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AActB;;;;;AARJ,IAAM,OAAO;AAMb,IAAM,QAAc,2KAAA,CAAqC,CAAC,OAAO,iBAAiB;IAChF,OACE,aAAA,GAAA,IAAA,6KAAA,EAAC,qLAAA,CAAU,KAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,aAAa,CAAC,UAAU;gBAKtB;YAHA,MAAM,SAAS,MAAM,MAAA;YACrB,IAAI,OAAO,OAAA,CAAQ,iCAAiC,EAAG,CAAA;aAEvD,qBAAA,MAAM,WAAA,cAAN,yCAAA,wBAAA,OAAoB,KAAK;YAEzB,IAAI,CAAC,MAAM,gBAAA,IAAoB,MAAM,MAAA,GAAS,EAAG,CAAA,MAAM,cAAA,CAAe;QACxE;IAAA;AAGN,CAAC;AAED,MAAM,WAAA,GAAc;AAIpB,IAAM,OAAO", "debugId": null}}]}