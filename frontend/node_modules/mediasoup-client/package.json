{"name": "mediasoup-client", "version": "3.15.4", "description": "mediasoup client side TypeScript library", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>> (https://inakibaz.me)", "<PERSON> <<EMAIL>> (https://github.com/jmillan)"], "license": "ISC", "homepage": "https://mediasoup.org", "repository": {"type": "git", "url": "git+https://github.com/versatica/mediasoup-client.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mediasoup"}, "main": "lib/index.js", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./types": {"types": "./lib/types.d.ts", "default": "./lib/types.js"}, "./ortc": {"ortc": "./lib/ortc.d.ts", "default": "./lib/ortc.js"}, "./enhancedEvents": {"ortc": "./lib/enhancedEvents.d.ts", "default": "./lib/enhancedEvents.js"}, "./handlers/*": {"types": "./lib/handlers/*.d.ts", "default": "./lib/handlers/*.js"}, "./fakeParameters": {"types": "./lib/test/fakeParameters.d.ts", "default": "./lib/test/fakeParameters.js"}}, "files": ["LICENSE", "README.md", "npm-scripts.mjs", "lib"], "engines": {"node": ">=18"}, "keywords": ["webrtc", "ortc", "browser", "nodejs"], "scripts": {"prepare": "node npm-scripts.mjs prepare", "typescript:build": "node npm-scripts.mjs typescript:build", "typescript:watch": "node npm-scripts.mjs typescript:watch", "lint": "node npm-scripts.mjs lint", "format": "node npm-scripts.mjs format", "test": "node npm-scripts.mjs test", "coverage": "node npm-scripts.mjs coverage", "release:check": "node npm-scripts.mjs release:check", "release": "node npm-scripts.mjs release"}, "dependencies": {"@types/debug": "^4.1.12", "@types/events-alias": "npm:@types/events@^3.0.3", "awaitqueue": "^3.2.4", "debug": "^4.4.1", "events-alias": "npm:events@^3.3.0", "fake-mediastreamtrack": "^2.1.4", "h264-profile-level-id": "^2.2.3", "sdp-transform": "^2.15.0", "supports-color": "^10.2.0", "ua-parser-js": "^2.0.4"}, "devDependencies": {"@eslint/js": "^9.34.0", "@types/jest": "^30.0.0", "@types/node": "^24.3.0", "@types/sdp-transform": "^2.15.0", "@types/ua-parser-js": "^0.7.39", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-prettier": "^5.5.4", "globals": "^16.3.0", "jest": "^30.1.0", "open-cli": "^8.0.0", "prettier": "^3.6.2", "ts-jest": "^29.4.1", "typescript": "^5.9.2", "typescript-eslint": "^8.41.0"}}