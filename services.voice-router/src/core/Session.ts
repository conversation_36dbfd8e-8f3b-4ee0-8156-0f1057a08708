import { types as mediasoupTypes } from 'mediasoup';
import { WebSocket } from 'ws';

import config from '../config';
import { Participant } from './Participant';
import { kafkaProducer } from '../services/KafkaProducer';
import net from 'node:net';
import dns from 'node:dns/promises';


interface ForkTarget {
  label: 'caller' | 'operator';
  ip: string;
  port: number;
}

interface WebRtcTransportInfo {
  id: string;
  iceParameters: mediasoupTypes.IceParameters;
  iceCandidates: mediasoupTypes.IceCandidate[];
  dtlsParameters: mediasoupTypes.DtlsParameters;
}


const resolveIp = async (ipOrHost: string): Promise<string> => {
  return net.isIP(ipOrHost) ? ipOrHost : (await dns.lookup(ipOrHost, { family: 4 })).address;
};

/**
 * Represents a single media session, encapsulating a MediaSoup Router
 * and managing all its participants.
 */
export class Session {
  public readonly id: string;
  private router: mediasoupTypes.Router;
  private worker: mediasoupTypes.Worker;
  private participants = new Map<string, Participant>();
  private forkingTransports = new Map<string, mediasoupTypes.Transport>();
  private forkingConsumers = new Map<string, mediasoupTypes.Consumer>();
  private producerRoles = new Set<string>();
  private hasFiredReadyForForkingEvent = false;

  constructor(worker: mediasoupTypes.Worker) {
    this.worker = worker;
    this.router = null!;
    this.id = '';
  }

  public async start(): Promise<void> {
    this.router = await this.worker.createRouter({
      mediaCodecs: config.mediasoup.router.mediaCodecs,
    });
    (this as { id: string }).id = this.router.id;
  }

  /**
   * ========================================================
   * Participant Management
   * ========================================================
   */

  /**
   * Adds a new participant to this session.
   */
  public addParticipant(participantId: string, ws: WebSocket): void {
    const participant = new Participant(participantId, ws);
    this.participants.set(participantId, participant);
  }

  /**
   * Removes a participant from this session.
   */
  public removeParticipant(participantId: string): void {
    const participant = this.participants.get(participantId);
    if (participant) {
      participant.close();
      this.participants.delete(participantId);
    }
  }

  /**
   * ========================================================
   * Media Logic
   * ========================================================
   */

  /**
   * Returns the RTP capabilities of the router.
   * This is called when a client first connects to retrieve the server's media capabilities.
   */
  public getRouterRtpCapabilities(): mediasoupTypes.RtpCapabilities {
    return this.router.rtpCapabilities;
  }

  /**
   * Creates a new WebRTC transport for a participant.
   * This is called when a client first connects and needs a transport to send/receive media.
   */
  public async createWebRtcTransport(options: { participantId: string }): Promise<WebRtcTransportInfo> {
    const participant = this.participants.get(options.participantId);
    if (!participant) throw new Error(`Participant ${options.participantId} not found`);

    const transport = await this.router.createWebRtcTransport(config.mediasoup.webRtcTransport);
    participant.addTransport(transport);

    return {
      id: transport.id,
      iceParameters: transport.iceParameters,
      iceCandidates: transport.iceCandidates,
      dtlsParameters: transport.dtlsParameters,
    };
  }

  /**
   * Connects a WebRTC transport for a participant.
   * This is called after the client has created a transport and is ready to connect it.
   */
  public async connectWebRtcTransport(options: {
    participantId: string;
    transportId: string;
    dtlsParameters: mediasoupTypes.DtlsParameters;
  }): Promise<void> {
    const participant = this.participants.get(options.participantId);
    if (!participant) throw new Error(`Participant ${options.participantId} not found`);
    await participant.connectTransport(options.transportId, options.dtlsParameters);
  }

  /**
   * Creates a new producer for a participant.
   * This is called when a client starts sending voice and notifies other participants.
   */
  public async createProducer(options: {
    participantId: string;
    transportId: string;
    kind: mediasoupTypes.MediaKind;
    rtpParameters: mediasoupTypes.RtpParameters;
    appData: any;
  }): Promise<string> {
    const participant = this.participants.get(options.participantId);
    if (!participant) throw new Error(`Participant ${options.participantId} not found`);

    const producer = await participant.createProducer({...options });

    const role = producer.appData?.role;
    if (role &&!this.producerRoles.has(role)) {
      console.log(`Producer with role '${role}' created for session ${this.id}`);
      this.producerRoles.add(role);
      // Check if the session is now ready
      this.checkForForkingReadiness();
    }

    this.participants.forEach((otherParticipant) => {
      if (otherParticipant.id!== participant.id) {
        otherParticipant.notify('newProducer', {
          participantId: participant.id,
          producerId: producer.id,
        });
      }
    });

    return producer.id;
  }

  /**
   * Creates a new consumer for a participant to receive a specific producer's media.
   * This is called when a client requests to receive a specific producer's media.
   */
  public async createConsumer(options: {
    participantId: string;
    transportId: string;
    producerId: string;
    rtpCapabilities: mediasoupTypes.RtpCapabilities;
  }) {
    const consumingParticipant = this.participants.get(options.participantId);
    if (!consumingParticipant) throw new Error(`Consuming participant ${options.participantId} not found`);

    // The server must check if the router can consume the producer
    if (!this.router.canConsume({ producerId: options.producerId, rtpCapabilities: options.rtpCapabilities })) {
      throw new Error(`Router cannot consume producer ${options.producerId}`);
    }

    const consumer = await consumingParticipant.createConsumer({
      transportId: options.transportId,
      producerId: options.producerId,
      rtpCapabilities: options.rtpCapabilities,
    });

    return {
      id: consumer.id,
      producerId: consumer.producerId,
      kind: consumer.kind,
      rtpParameters: consumer.rtpParameters,
    };
  }

  /**
   * Resumes a specific consumer to start receiving media.
   * This is called after the client has created a consumer and is ready to start receiving media.
   */
  public async resumeConsumer(options: { participantId: string; consumerId: string }): Promise<void> {
    const participant = this.participants.get(options.participantId);
    if (!participant) throw new Error(`Participant ${options.participantId} not found`);
    await participant.resumeConsumer(options.consumerId);
  }

  /**
   * Pauses a specific producer (e.g., user mutes their mic).
   */
  public async pauseProducer(options: { participantId: string; producerId: string }): Promise<void> {
    const participant = this.participants.get(options.participantId);
    if (!participant) throw new Error(`Participant ${options.participantId} not found`);
    await participant.pauseProducer(options.producerId);
  }

  /**
   * Resumes a specific producer (e.g., user unmutes their mic).
   */
  public async resumeProducer(options: { participantId: string; producerId: string }): Promise<void> {
    const participant = this.participants.get(options.participantId);
    if (!participant) throw new Error(`Participant ${options.participantId} not found`);
    await participant.resumeProducer(options.producerId);
  }

  /**
   * Checks if both 'caller' and 'operator' producers exist and fires an event.
   */
  private async checkForForkingReadiness(): Promise<void> {
    if (this.hasFiredReadyForForkingEvent) {
      return;
    }

    const hasCaller = this.producerRoles.has('caller');
    const hasOperator = this.producerRoles.has('operator');

    if (hasCaller && hasOperator) {
      this.hasFiredReadyForForkingEvent = true;
      console.log(`Session ${this.id} is ready for forking. Firing event.`);

      const eventPayload = {
        event_type: 'media.producers.ready',
        source_service: 'voice-router',
        media_session_id: this.id,
      };

      await kafkaProducer.publish('media.producers.ready', eventPayload);
    }
  }

  /**
   * Forks all audio producers in the session to specified UDP endpoints.
   */
  public async startForking(targets: ForkTarget[]): Promise<Array<{ label: string; ssrc: number }>> {
    const producers = Array.from(this.participants.values()).flatMap(p => Array.from(p.getProducers().values()));
    const audioProducers = producers.filter(p => p.kind === 'audio');
    const results: Array<{ label: string; ssrc: number }> = [];

    for (const producer of audioProducers) {
      const role = producer.appData.role as 'caller' | 'operator';
      const target = targets.find(t => t.label === role);

      if (!target) {
        console.warn(`No fork target found for producer with role: ${role}`);
        continue;
      }

      // Create a PlainTransport to send RTP to the AI service
      const forkTransport = await this.router.createPlainTransport({
        listenIp: '0.0.0.0', // Mediasoup will find a suitable outbound IP
        rtcpMux: true,
        comedia: false,
      });

      // Connect the transport to the AI service's UDP listener
      const remoteIp = await resolveIp(target.ip);
      await forkTransport.connect({
        ip: remoteIp,
        port: target.port,
      });

      // Create a consumer on the new transport to receive the producer's audio
      const forkConsumer = await forkTransport.consume({
        producerId: producer.id,
        rtpCapabilities: this.router.rtpCapabilities, // Use router's capabilities for simplicity
        paused: false,
      });

      // Store for cleanup
      this.forkingTransports.set(forkConsumer.id, forkTransport);
      this.forkingConsumers.set(forkConsumer.id, forkConsumer);

      // Resume the consumer to start forwarding RTP packets
      await forkConsumer.resume();

      const encoding = (forkConsumer.rtpParameters as any)?.encodings?.[0];
      const ssrc = encoding?.ssrc as number | undefined;

      results.push({
        label: role,
        ssrc: ssrc!,
      });

      console.log(`Forking producer ${producer.id} (role: ${role}) to ${remoteIp}:${target.port} with SSRC ${ssrc}`);
    }

    return results;
  }

  /**
   * Closes the session and all its associated resources.
   */
  public close(): void {
    if (this.router) {
      this.router.close();
      this.router = null!;
      console.log(`Session ${this.id} closed.`);
    }
  }
}