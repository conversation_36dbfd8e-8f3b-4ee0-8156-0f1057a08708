from typing import Any, Callable
import asyncio

from cortexacommon.config import KafkaSettings
from cortexacommon.logging import get_logger
from cortexacommon.events.consumer import EventConsumer
from cortexacommon.events.schemas import CallAssignedEvent, WebRTCConnectionDetailsEvent, TranscriptionEvent

from services.connection_manager import ConnectionManager


logger = get_logger(__name__)


class KafkaEventRouter:
    """
    Encapsulates a Kafka EventConsumer and routes events to WebSocket connections.

    - Call start() to begin consuming.
    - Call stop() to stop consuming and cleanup.

    Handlers can be extended easily by registering additional topic->handler pairs.
    """

    def __init__(self, connections: ConnectionManager, kafka_settings: KafkaSettings, topics: list[str] | None = None):
        self._connections = connections
        self._kafka_settings = kafka_settings
        self._topics = topics or ["call.assigned", "webrtc.connection.details", "transcription.completed"]
        self._consumer: EventConsumer | None = None
        self._consume_task: asyncio.Task | None = None
        # Map call_id -> operator_id to route transcription events
        self._call_to_operator: dict[str, str] = {}

    async def start(self) -> None:
        self._consumer = EventConsumer(
            kafka_settings=self._kafka_settings,
            topics=self._topics,
        )
        await self._consumer.start()

        # Register built-in handlers
        self._consumer.register_handler("call.assigned", self.handle_call_assigned)
        self._consumer.register_handler("webrtc.connection.details", self.handle_webrtc_connection_details)
        self._consumer.register_handler("transcription.completed", self.handle_transcription_completed)

        # Start consumption loop
        self._consume_task = asyncio.create_task(self._consumer.consume())
        logger.info(f"KafkaEventRouter started and consuming topics: {self._topics}")

    async def stop(self) -> None:
        if self._consumer:
            await self._consumer.stop()
            if self._consume_task:
                self._consume_task.cancel()
            self._consumer = None
            self._consume_task = None

    async def handle_call_assigned(self, payload: dict[str, Any]) -> None:
        event = CallAssignedEvent(**payload)
        await self._connections.send_to(event.operator_id, payload)

    async def handle_webrtc_connection_details(self, payload: dict[str, Any]) -> None:
        event = WebRTCConnectionDetailsEvent(**payload)
        # Keep mapping of call -> operator for routing later transcription events
        self._call_to_operator[event.call_id] = event.operator_id
        await self._connections.send_to(event.operator_id, payload)

    async def handle_transcription_completed(self, payload: dict[str, Any]) -> None:
        event = TranscriptionEvent(**payload)
        operator_id = self._call_to_operator.get(event.call_id)
        if not operator_id:
            logger.info(f"No operator mapping for call {event.call_id}; dropping transcription event.")
            return
        await self._connections.send_to(operator_id, payload)

    # Optional: helper to add external/custom handlers in future
    def register_handler(self, topic: str, handler: Callable[[dict[str, Any]], Any]) -> None:
        if not self._consumer:
            raise RuntimeError("Router not started; cannot register handler yet.")
        self._consumer.register_handler(topic, handler)

