import asyncio

from cortexacommon.logging import get_logger
from cortexacommon.audio.session_manager import SessionManager

from config import settings
from context import ApplicationContext
from consumers.call_connected import setup_call_connected_consumer

logger = get_logger(__name__)


async def run() -> None:
    """
    Initializes the application context, sets up service components,
    and runs the main event loop.
    """
    logger.info("Starting AI service")

    context = await ApplicationContext.create()

    session_manager = SessionManager(
        voice_router_client=context.voice_router_client,
        handler=context.utterance_detector,
        audio_settings=settings.audio,
        service_ip=settings.service_ip,
    )

    consumer, consume_task = await setup_call_connected_consumer(
        handler=session_manager.handle_call_connected
    )

    logger.info("AI service started successfully and is ready for events.")

    try:
        await asyncio.Event().wait()
    except (KeyboardInterrupt, asyncio.CancelledError):
        logger.info("Shutdown signal received.")
    finally:
        logger.info("Starting graceful shutdown...")
        if consumer:
            await consumer.stop()
        if consume_task:
            consume_task.cancel()

        await session_manager.shutdown()
        await context.cleanup()
        logger.info("Shutdown complete.")


if __name__ == "__main__":
    asyncio.run(run())