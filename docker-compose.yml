services:
  # ------------------------------------------------------------------
  # Cortexa Microservices Platform
  # ------------------------------------------------------------------

  user-management:
    image: cortexa/user-management:latest
    container_name: cortexa-user-management
    ports:
      - "8000:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
    extra_hosts:
      # This is needed for the container to connect to Supabase on the host
      # Could add `supabase_network_infra` network to supabase and remove this
      - "host.docker.internal:host-gateway"
    networks:
      - api-gateway
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.user-management.rule=Host(`localhost`) && PathPrefix(`/users`)"
      - "traefik.http.routers.user-management.entrypoints=web"
      - "traefik.http.routers.user-management.middlewares=cors-headers@file,jwt-auth-role-administrator@file"
      - "traefik.http.services.user-management.loadbalancer.server.port=8000"

  participant-presence:
    image: cortexa/participant-presence:latest
    container_name: cortexa-participant-presence
    ports:
      - "8001:8001"
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
    depends_on:
      kafka:
        condition: service_healthy
    networks:
      - api-gateway
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.participant-presence.rule=Host(`localhost`) && PathPrefix(`/presence`)"
      - "traefik.http.routers.participant-presence.entrypoints=web"
      - "traefik.http.routers.participant-presence.middlewares=jwt-auth-authenticated@file"
      - "traefik.http.services.participant-presence.loadbalancer.server.port=8001"

  call-orchestrator:
    image: cortexa/call-orchestrator:latest
    container_name: cortexa-call-orchestrator
    ports:
      - "8002:8002"
    environment:
      - HOST=0.0.0.0
      - PORT=8002
      - DB_URI=postgresql+psycopg://user:password@postgres:5432/cortexa
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - VOICE_ROUTER_URL=http://voice-router:3001
      - SIP_GATEWAY_URL=http://sip-gateway:8010
    depends_on:
      kafka:
        condition: service_healthy
      sip-gateway:
        condition: service_healthy
      # voice-router:
      #   condition: service_healthy
    networks:
      - api-gateway
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.call-orchestrator.rule=Host(`localhost`) && PathPrefix(`/calls`)"
      - "traefik.http.routers.call-orchestrator.entrypoints=web"
      - "traefik.http.routers.call-orchestrator.middlewares=cors-headers@file,jwt-auth-authenticated@file"
      - "traefik.http.services.call-orchestrator.loadbalancer.server.port=8002"

  queuing-service:
    image: cortexa/queuing-service:latest
    container_name: cortexa-queuing-service
    environment:
      - PARTICIPANT_SERVICE_BASE_URL=http://participant-presence:8001
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
    depends_on:
      kafka:
        condition: service_healthy
      participant-presence:
        condition: service_healthy
    networks:
      - api-gateway

  websocket-bridge:
    image: cortexa/websocket-bridge:latest
    container_name: cortexa-websocket-bridge
    ports:
      - "8004:8004"
    depends_on:
      kafka:
        condition: service_healthy
    environment:
      - HOST=0.0.0.0
      - PORT=8004
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
    networks:
      - api-gateway
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.websocket-bridge.rule=Host(`localhost`) && PathPrefix(`/bridge`)"
      - "traefik.http.routers.websocket-bridge.entrypoints=web"
      - "traefik.http.routers.websocket-bridge.middlewares=jwt-auth-authenticated@file"
      - "traefik.http.services.websocket-bridge.loadbalancer.server.port=8004"

  voice-router:
    image: cortexa/voice-router:latest
    container_name: cortexa-voice-router
    ports:
      - "3001:3001"
      - "60000-60099:60000-60099/udp"
      - "60000-60099:60000-60099/tcp"
    environment:
      - LISTEN_HOST=0.0.0.0
      - LISTEN_PORT=3001
      - MEDIASOUP_WORKERS=4
      - MEDIASOUP_LISTEN_IP=0.0.0.0
      - MEDIASOUP_ANNOUNCED_IP=*************
      - MEDIASOUP_RTC_MIN_PORT=60000
      - MEDIASOUP_RTC_MAX_PORT=60099
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
    depends_on:
      kafka:
        condition: service_healthy
    networks:
      - api-gateway
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.voice-router.rule=Host(`localhost`) && PathPrefix(`/voice`)"
      - "traefik.http.routers.voice-router.entrypoints=web"
      - "traefik.http.routers.voice-router.middlewares=cors-headers@file"
      - "traefik.http.services.voice-router.loadbalancer.server.port=3001"

  sip-gateway:
    image: cortexa/sip-gateway:latest
    container_name: cortexa-sip-gateway
    ports:
      - "8010:8010"
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
    depends_on:
      kafka:
        condition: service_healthy
    networks:
      - api-gateway

  ai:
    image: cortexa/ai:latest
    container_name: cortexa-ai
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - VOICE_ROUTER_URL=http://voice-router:3001
      - AI_SERVICE_IP=ai
      - LOG_LEVEL=DEBUG
      - WHISPER_DEVICE=cuda
      - WHISPER_COMPUTE_TYPE=float16
    depends_on:
      kafka:
        condition: service_healthy
    networks:
      - api-gateway
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

  # =========== sidecars ===========

  # ------------------------------------------------------------------
  # Traefik Reverse Proxy
  #
  # What: Reverse proxy for all services, provides load balancing, SSL termination, and JWT authentication
  # Why: Centralized point of entry for all services, simplifies service discovery and routing
  # ------------------------------------------------------------------

  traefik:
    image: "traefik:v3.5"
    container_name: cortexa-traefik
    ports:
      - "80:80"
      - "8080:8080"
    command:
      #- "--log.level=DEBUG"
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--providers.docker.network=api-gateway"
      - "--providers.file.directory=/configuration/"
      - "--providers.file.watch=true"
      - "--entrypoints.web.address=:80"
      # Enable the experimental plugin feature and specify the JWT middleware
      - "--experimental.plugins.jwt.modulename=github.com/agilezebra/jwt-middleware"
      - "--experimental.plugins.jwt.version=v1.3.2"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "./infra/traefik:/configuration/"
    networks:
      - api-gateway

  # ------------------------------------------------------------------
  # Kafka and Zookeeper
  #
  # What: Message queue for inter-service communication
  # Why: Decouples services, enables asynchronous communication, and provides fault tolerance
  # ------------------------------------------------------------------

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: cortexa-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - api-gateway

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: cortexa-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:19092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: INTERNAL:PLAINTEXT,EXTERNAL:PLAINTEXT
      KAFKA_LISTENERS: INTERNAL://0.0.0.0:9092,EXTERNAL://0.0.0.0:19092
      KAFKA_ADVERTISED_LISTENERS: INTERNAL://kafka:9092,EXTERNAL://localhost:9092
      KAFKA_INTER_BROKER_LISTENER_NAME: INTERNAL
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "kafka:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - api-gateway

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: cortexa-kafka-ui
    depends_on:
      kafka:
        condition: service_healthy
    ports:
      - "9093:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:9092
    networks:
      - api-gateway

  # ------------------------------------------------------------------
  # Database
  #
  # What: PostgreSQL database for persistent data storage
  # Why: Centralized data storage for all services
  # ------------------------------------------------------------------

  postgres:
    image: postgres:15.4-alpine
    container_name: cortexa-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=cortexa
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - api-gateway

  # ------------------------------------------------------------------
  # Monitoring
  #
  # What: Observability tools for monitoring and tracing
  # Why: Provides insights into service health, performance, and dependencies
  # ------------------------------------------------------------------

  # prometheus:
  #   image: prom/prometheus:v2.47.0
  #   container_name: cortexa-prometheus
  #   ports:
  #     - "9090:9090"
  #   volumes:
  #     - ./infra/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
  #     - prometheus_data:/prometheus
  #   command:
  #     - '--config.file=/etc/prometheus/prometheus.yml'
  #     - '--storage.tsdb.path=/prometheus'
  #     - '--web.console.libraries=/etc/prometheus/console_libraries'
  #     - '--web.console.templates=/etc/prometheus/consoles'
  #     - '--storage.tsdb.retention.time=200h'
  #     - '--web.enable-lifecycle'
  #   networks:
  #     - internal

  # grafana:
  #   image: grafana/grafana:10.1.0
  #   container_name: cortexa-grafana
  #   volumes:
  #     - grafana_data:/var/lib/grafana
  #     #- ./monitoring/grafana/provisioning:/etc/grafana/provisioning
  #     #- ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
  #   environment:
  #     - GF_SECURITY_ADMIN_USER=admin
  #     - GF_SECURITY_ADMIN_PASSWORD=admin
  #     - GF_USERS_ALLOW_SIGN_UP=false
  #   networks:
  #     - internal
  #     - api-gateway


volumes:
  prometheus_data:
  grafana_data:
  postgres_data:


networks:
  api-gateway:
    driver: bridge
  # No longer using internal because two networks was not playing nice with Traefik
  # internal:
  #   driver: bridge
