from contextlib import asynccontextmanager
from fastapi import FastAPI

from config import settings
from context import ApplicationContext
from api.v1.bridge.router import router


@asynccontextmanager
async def lifespan(app: FastAPI):
    context = await ApplicationContext.create()
    app.state.context = context
    yield
    await context.cleanup()


app = FastAPI(
    title="WebSocket Bridge",
    version="0.1.0",
    lifespan=lifespan,
    host=settings.host,
    port=settings.port,
)

app.include_router(router)

