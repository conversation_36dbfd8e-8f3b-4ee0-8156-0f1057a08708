import numpy as np
import torch
import math

from cortexacommon.logging import get_logger

from config import settings
from .base import BaseVADProvider

logger = get_logger(__name__)


class SileroVADProvider(BaseVADProvider):
    """Voice Activity Detection provider using Silero VAD (PyTorch Hub).

    Notes:
    - Expects 16 kHz, 16-bit PCM, mono input frames (bytes)
    - Returns True if speech prob > threshold
    - Maintains internal model state across sequential frames; reset when needed
    """

    def __init__(self, threshold: float = 0.5, frame_duration_ms: int = 30, sample_rate: int | None = None, **kwargs):
        super().__init__(**kwargs)
        self.threshold = threshold
        self.sample_rate = sample_rate or settings.audio.sample_rate
        self.frame_duration_ms = frame_duration_ms
        computed = int(self.sample_rate * frame_duration_ms / 1000)
        min_samples = math.ceil(self.sample_rate / 31.25)  # Silero minimum (~512 @ 16k)
        self._frame_size = max(computed, min_samples)
        self._model: torch.jit.ScriptModule | None = None

    async def initialize(self) -> None:
        """Load Silero VAD model from torch.hub and prepare for streaming use."""
        try:
            torch.set_num_threads(1)
            model, utils = torch.hub.load(
                repo_or_dir="snakers4/silero-vad",
                model="silero_vad",
                force_reload=False,
                onnx=False,
                trust_repo=True,
            )
            self._model = model
            self._initialized = True
            eff_ms = int(self._frame_size * 1000 / self.sample_rate)
            logger.info("Silero VAD initialized (threshold=%.2f, frame≈%dms, samples=%d)", self.threshold, eff_ms, self._frame_size)
        except Exception as e:
            logger.error(f"Failed to initialize Silero VAD: {e}")
            raise

    def _bytes_to_float_tensor(self, audio_frame: bytes) -> torch.Tensor:
        """Convert 16-bit PCM bytes to normalized float32 torch tensor (mono)."""
        # int16 -> float32 in [-1, 1]
        np_samples = np.frombuffer(audio_frame, dtype=np.int16).astype(np.float32) / 32768.0
        if np_samples.ndim != 1:
            np_samples = np_samples.reshape(-1)
        # Create 1D tensor; silero expects [T] (streaming) or [B, T]
        return torch.from_numpy(np_samples)

    def is_speech(self, audio_frame: bytes) -> bool:
        """Return True if the buffer contains any speech according to Silero VAD.

        Accepts arbitrary-length buffers; processes them in contiguous windows of
        size `frame_size` (in samples). Returns True if any window exceeds threshold.
        """
        assert self._model is not None, "Silero VAD not initialized"

        frame_bytes = self.frame_size * 2  # 2 bytes per int16 sample
        n = len(audio_frame)
        if n < frame_bytes:
            return False

        try:
            with torch.no_grad():
                # Iterate over non-overlapping windows; adjust step for overlap if desired
                for start in range(0, n - frame_bytes + 1, frame_bytes):
                    window = audio_frame[start : start + frame_bytes]
                    wav = self._bytes_to_float_tensor(window)
                    prob = self._model(wav, self.sample_rate).item()
                    if prob >= self.threshold:
                        return True
        except Exception as e:
            logger.warning(f"Silero VAD inference error: {e}")
            return False

        return False

    @property
    def frame_size(self) -> int:
        return self._frame_size

    def reset(self) -> None:
        """Reset model streaming states (call when starting a new stream)."""
        if self._model and hasattr(self._model, "reset_states"):
            self._model.reset_states()

