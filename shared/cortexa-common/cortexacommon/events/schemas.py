from datetime import datetime, timezone
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class BaseEvent(BaseModel):
    """Base event schema."""

    event_id: UUID = Field(default_factory=uuid4, description="Unique event identifier")
    event_type: str = Field(description="Type of the event")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Event timestamp")
    source_service: str = Field(default="unknown", description="Service that generated the event")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
        }


# ---


class SipCallReceivedEvent(BaseEvent):
    """Event model for sip.call.received events."""

    source_service: str = "sip-gateway"
    event_type: str = "sip.call.received"
    call_id: str


class CallStartedEvent(BaseEvent):
    """Event model for call.started events."""

    source_service: str = "call-orchestrator"
    event_type: str = "call.started"
    call_id: str


class ParticipantStatusChangedEvent(BaseEvent):
    """Event model for participant.status.changed events."""

    source_service: str = "participant-presence"
    event_type: str = "participant.status.changed"
    participant_id: str
    status: str


class CallQueuedEvent(BaseEvent):
    """Event model for call.queued events."""

    source_service: str = "queuing-service"
    event_type: str = "call.queued"
    call_id: str


class CallAssignedEvent(BaseEvent):
    """Event model for call.assigned events."""

    source_service: str = "queuing-service"
    event_type: str = "call.assigned"
    call_id: str
    operator_id: str


class CallConnectingEvent(BaseEvent):
    """Event model for call.connecting events."""

    source_service: str = "call-orchestrator"
    event_type: str = "call.connecting"
    call_id: str
    operator_id: str
    media_session_id: str


class CallConnectedEvent(BaseEvent):
    """Event from call orchestrator enriched with call and operator IDs from session participant ready event."""

    source_service: str = "call-orchestrator"
    event_type: str = "call.connected"
    media_session_id: str
    call_id: str
    operator_id: str


class WebRTCConnectionDetailsEvent(BaseEvent):
    """Event model for webrtc.connection.details events."""

    source_service: str = "call-orchestrator"
    event_type: str = "webrtc.connection.details"
    call_id: str
    operator_id: str
    media_session_id: str


class TranscriptionEvent(BaseEvent):
    """Event published when a transcription is completed."""

    source_service: str = "ai"
    event_type: str = Field(default="transcription.completed", description="Event type")
    call_id: str = Field(description="Unique call identifier")
    participant_id: str = Field(description="Unique participant identifier")
    segment_id: str = Field(description="Unique segment identifier")
    text: str = Field(description="Original transcribed text")
    confidence: float = Field(description="Confidence score of transcription")


class MediaProducersReadyEvent(BaseEvent):
    """Event model for media.producers.ready events."""

    source_service: str = "voice-router"
    event_type: str = "media.producers.ready"
    media_session_id: str
