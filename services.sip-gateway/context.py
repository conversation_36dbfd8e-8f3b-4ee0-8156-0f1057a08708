from config import settings
from dataclasses import dataclass

from core.sessions import <PERSON><PERSON>ana<PERSON>

from cortexacommon.events.producer import EventProducer


from dataclasses import dataclass


@dataclass
class ApplicationContext:
    """
    Application context for managing shared resources.
    """

    event_producer: EventProducer
    session_manager: <PERSON><PERSON>anager

    @classmethod
    async def create(cls) -> 'ApplicationContext':
        """Create and initialize a new ApplicationContext instance."""
        try:
            event_producer = EventProducer(settings.kafka_settings)
            await event_producer.start()

            session_manager = SessionManager()

            return cls(
                event_producer=event_producer,
                session_manager=session_manager,
            )
        except Exception as e:
            raise RuntimeError(f"Failed to initialize application context: {e}")

    async def cleanup(self) -> None:
        """Clean up the application context and its resources."""
        try:
            if self.event_producer:
                await self.event_producer.stop()
        except Exception as e:
            print(f"Error during application context cleanup: {e}")
