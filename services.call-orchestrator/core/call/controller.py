from dataclasses import dataclass

from cortexacommon.logging import get_logger
from cortexacommon.events.producer import EventProducer
from cortexacommon.events.schemas import (
    CallStartedEvent,
    CallConnectingEvent,
    CallConnectedEvent,
    WebRTCConnectionDetailsEvent,
)
from cortexacommon.clients.voice_router import VoiceRouterClient
from cortexacommon.clients.sip_gateway import SIPGatewayService

from config import settings
from core.db import Database
from core.call.repositories import CallRepository

logger = get_logger(__name__)


@dataclass
class CallController:
    """
    High-level wrapper that orchestrates call lifecycle using repositories and services.
    """
    db: Database
    sip_gateway: SIPGatewayService
    voice_router: VoiceRouterClient
    producer: EventProducer

    @classmethod
    async def create(
        cls, db: Database, sip_gateway: SIPGatewayService, voice_router: VoiceRouterClient,
    ) -> "CallController":
        # Create event producer
        producer = EventProducer(settings.kafka_settings)
        await producer.start()

        return cls(db=db, sip_gateway=sip_gateway, voice_router=voice_router, producer=producer)

    @property
    def call_repository(self) -> CallRepository:
        return CallRepository(self.db.session_factory)

    async def cleanup(self) -> None:
        if self.producer:
            await self.producer.stop()

    async def on_sip_call_received(self, call_id: str) -> None:
        """
        Handle incoming SIP call events.
        """
        event = CallStartedEvent(call_id=call_id)
        await self.producer.publish_event("call.started", event)

    async def accept_call(self, call_id: str, operator_id: str) -> None:
        """
        Accept a call by:
        1) Creating a media session
        2) Connecting the SIP call to the media session
        3) Publishing webrtc.connection.details event
        # TODO: Turn this function into a SAGA transaction pattern
        """

        media_session_id = await self.voice_router.internal_create_media_session()

        # Ensure call exists and set media session and operator
        repo = self.call_repository
        await repo.create_if_not_exists(call_id)
        await repo.set_media_session(call_id, media_session_id)
        await repo.set_operator(call_id, operator_id)

        # Publish WebRTC connection details for the frontend to establish WebRTC
        details_event = WebRTCConnectionDetailsEvent(
            call_id=call_id,
            operator_id=operator_id,
            media_session_id=media_session_id,
        )
        await self.producer.publish_event("webrtc.connection.details", details_event, key=operator_id)

        # Connect the SIP call to the media session
        await self.sip_gateway.connect_call(call_id, media_session_id)
        await repo.set_status(call_id, "active")

        # Notify that the call is connecting
        connecting_event = CallConnectingEvent(call_id=call_id, operator_id=operator_id, media_session_id=media_session_id)
        await self.producer.publish_event("call.connecting", connecting_event, key=call_id)

    async def on_voice_participants_ready(self, media_session_id: str) -> None:
        """
        Handle media.producers.ready events.
        """
        logger.info(f"Received media producers ready for media session {media_session_id}")
        call = await self.call_repository.get_by_media_session_id(media_session_id)

        if not call:
            raise ValueError(f"Call not found for media session {media_session_id}")
        
        if not call.operator_id:
            raise ValueError(f"Operator not set for call {call.id}")

        event = CallConnectedEvent(call_id=call.id, operator_id=call.operator_id, media_session_id=media_session_id)
        await self.producer.publish_event("call.connected", event)
