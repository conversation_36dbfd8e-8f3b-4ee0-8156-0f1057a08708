FROM nvidia/cuda:12.1-devel-ubuntu22.04

# Install Python 3.12
RUN apt-get update && apt-get install -y \
    software-properties-common \
    && add-apt-repository ppa:deadsnakes/ppa \
    && apt-get update && apt-get install -y \
    python3.12 \
    python3.12-dev \
    python3.12-distutils \
    python3-pip \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Set Python 3.12 as default
RUN update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.12 1
RUN update-alternatives --install /usr/bin/python python /usr/bin/python3.12 1

WORKDIR /app

# Install Poetry
RUN python3 -m pip install --no-cache-dir poetry

# Copy shared common first to leverage Docker layer caching
COPY shared/cortexa-common /app/shared/cortexa-common

# Copy service files
COPY services.ai /app/services.ai

WORKDIR /app/services.ai

# Install dependencies
RUN poetry install --no-interaction --no-ansi

# Run the service
CMD ["poetry", "run", "python3", "main.py"]
