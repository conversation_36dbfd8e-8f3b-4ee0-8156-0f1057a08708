#!/usr/bin/env python3
"""
GPU availability check script for the AI service.
This script verifies that CUDA and GPU acceleration are available for Whisper.
"""

import sys
import torch
from faster_whisper import WhisperModel

def check_cuda():
    """Check CUDA availability."""
    print("=== CUDA Check ===")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"CUDA version: {torch.version.cuda}")
        print(f"GPU count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
            print(f"  Memory: {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB")
    else:
        print("No CUDA devices found")
    print()

def check_whisper_gpu():
    """Check if Whisper can use GPU."""
    print("=== Whisper GPU Check ===")
    try:
        # Try to initialize Whisper with CUDA
        model = WhisperModel("tiny", device="cuda", compute_type="float16")
        print("✅ Whisper model successfully initialized with CUDA")
        print(f"Model device: {model.device}")
        print(f"Compute type: {model.compute_type}")
        del model  # Clean up
    except Exception as e:
        print(f"❌ Failed to initialize Whisper with CUDA: {e}")
        
        # Fallback to CPU
        try:
            model = WhisperModel("tiny", device="cpu", compute_type="int8")
            print("✅ Whisper model successfully initialized with CPU fallback")
            del model
        except Exception as e2:
            print(f"❌ Failed to initialize Whisper with CPU: {e2}")
    print()

def main():
    """Main function."""
    print("GPU Availability Check for Cortexa AI Service")
    print("=" * 50)
    
    check_cuda()
    check_whisper_gpu()
    
    if torch.cuda.is_available():
        print("🎉 GPU acceleration is available and ready!")
        print("Recommended settings:")
        print("  WHISPER_DEVICE=cuda")
        print("  WHISPER_COMPUTE_TYPE=float16")
    else:
        print("⚠️  GPU acceleration not available, falling back to CPU")
        print("Recommended settings:")
        print("  WHISPER_DEVICE=cpu")
        print("  WHISPER_COMPUTE_TYPE=int8")

if __name__ == "__main__":
    main()
