import asyncio
from typing import Dict

from cortexacommon.logging import get_logger
from cortexacommon.events.schemas import CallConnectedEvent
from cortexacommon.clients.voice_router import VoiceRouterClient
from cortexacommon.audio.handler import BaseAudioPacketHandler
from cortexacommon.audio.session import Session
from cortexacommon.config import SessionAudioSettings


logger = get_logger(__name__)


class SessionManager:
    """
    Manages the lifecycle of all active call sessions for a service.
    (COMMON MODULE)
    """

    def __init__(
        self,
        handler: Base<PERSON>udioPacketHandler,
        voice_router_client: VoiceRouterClient,
        audio_settings: SessionAudioSettings,
        service_ip: str,
    ):
        self._handler = handler
        self._voice_router_client = voice_router_client
        self._audio_settings = audio_settings
        self._service_ip = service_ip
        self._sessions: Dict[str, Session] = {}
        logger.info("Common SessionManager initialized")

    async def handle_call_connected(self, event: CallConnectedEvent) -> None:
        """Creates a new session from a connection event."""
        if event.call_id in self._sessions:
            logger.warning(
                f"A session for call {event.call_id} already exists. Ignoring."
            )
            return

        participants = {"operator": event.operator_id, "caller": event.call_id}

        session = Session(
            call_id=event.call_id,
            media_session_id=event.media_session_id,
            participants=participants,
            handler=self._handler,
            voice_router_client=self._voice_router_client,
            audio_settings=self._audio_settings,
            service_ip=self._service_ip,
        )

        try:
            await session.start()
            self._sessions[event.call_id] = session
            logger.info(f"Successfully registered session for call {event.call_id}")
        except Exception as e:
            logger.error(f"Failed to start session for call {event.call_id}: {e}", exc_info=True)
            await session.stop()

    async def terminate_session(self, call_id: str) -> None:
        """Stops and removes a session."""
        session = self._sessions.pop(call_id, None)
        if session:
            await session.stop()

    async def shutdown(self) -> None:
        """Gracefully stops all active sessions."""
        logger.info(f"Shutting down {len(self._sessions)} active sessions.")
        shutdown_tasks = [
            session.stop() for session in self._sessions.values()
        ]
        await asyncio.gather(*shutdown_tasks, return_exceptions=True)
        self._sessions.clear()
