"use client"

import { useEffect, useRef, useState } from 'react'
import { wsBridge, WebrtcConnectionDetailsPayload, TranscriptionCompletedPayload } from '@/lib/services/wsbridge'
import { connectToVoiceRouter, VoiceRouterSession } from '@/lib/services/voice-router'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'

type ChatMsg = { id: string; text: string; side: 'operator' | 'caller'; confidence: number }

export function OperatorCall() {
  const [connected, setConnected] = useState(false)
  const [callId, setCallId] = useState<string | null>(null)
  const [operatorId, setOperatorId] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [messages, setMessages] = useState<ChatMsg[]>([])
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const sessionRef = useRef<VoiceRouterSession | null>(null)

  // Confidence indicator helpers
  function confidenceColor(conf: number): string {
    if (conf >= 0.85) return 'bg-green-500'
    if (conf >= 0.6) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const ConfidenceDot = ({ confidence }: { confidence: number }) => {
    const pct = Math.round((confidence ?? 0) * 100)
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <span
            className={`h-2.5 w-2.5 rounded-full ${confidenceColor(confidence)}`}
            aria-label={`Confidence ${pct}%`}
            title={`Confidence ${pct}%`}
          />
        </TooltipTrigger>
        <TooltipContent side="top">{pct}% confidence</TooltipContent>
      </Tooltip>
    )
  }

  const chatEndRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    function onDetails(payload: WebrtcConnectionDetailsPayload & { voice_router_http_url?: string }) {
      setCallId(payload.call_id)
      setOperatorId(payload.operator_id)
      setMessages([])
      setup(payload).catch((e) => setError(`${e}`))
    }

    function onTranscript(payload: TranscriptionCompletedPayload) {
      if (!callId || payload.call_id !== callId) return
      const side: 'operator' | 'caller' = payload.participant_id === operatorId ? 'operator' : 'caller'
      setMessages((prev) => [
        ...prev,
        { id: payload.segment_id, text: payload.text, side, confidence: payload.confidence }
      ])
      queueMicrotask(() => chatEndRef.current?.scrollIntoView({ behavior: 'smooth' }))
    }

    wsBridge.onWebrtcConnectionDetails(onDetails)
    wsBridge.onTranscriptionCompleted(onTranscript)
    wsBridge.connect()

    return () => {
      wsBridge.off('webrtc.connection.details', onDetails as unknown as import('@/lib/services/base').EventListener)
      wsBridge.off('transcription.completed', onTranscript as unknown as import('@/lib/services/base').EventListener)
      wsBridge.disconnect()
      teardown()
    }
  }, [callId, operatorId])

  async function setup(payload: WebrtcConnectionDetailsPayload & { voice_router_http_url?: string }) {
    try {
      setError(null)
      if (!audioRef.current) audioRef.current = new Audio()
      const httpBaseUrl = (payload.voice_router_http_url as string) || process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001'
      sessionRef.current = await connectToVoiceRouter({
        mediaSessionId: payload.media_session_id,
        httpBaseUrl,
        remoteAudioEl: audioRef.current,
      })
      setConnected(true)
    } catch (e) {
      setError(`${e}`)
      setConnected(false)
    }
  }

  function teardown() {
    try { sessionRef.current?.close() } catch { }
    sessionRef.current = null
    setConnected(false)
  }

  return (
    <>
      {callId && (
        <Card className="p-4 space-y-3">
          <div className="flex items-center justify-between">
            <div className="font-medium">Operator Call</div>
            {connected ? (
              <span className="text-green-600">Connected</span>
            ) : (
              <span className="text-amber-600">Waiting…</span>
            )}
          </div>
          {callId && <div className="text-xs text-muted-foreground">Call: {callId}</div>}
          {error && <div className="text-xs text-red-600">{error}</div>}
          <audio ref={audioRef} autoPlay playsInline />

          {/* Chat transcript */}
          <div className="mt-2 h-64 overflow-y-auto rounded-md border bg-background p-3 space-y-2">
            {messages.map((m) => (
              <div key={m.id} className={`flex ${m.side === 'operator' ? 'justify-end' : 'justify-start'}`}>
                <div className="flex items-center gap-2 max-w-[75%]">
                  {m.side !== 'operator' && <ConfidenceDot confidence={m.confidence} />}
                  <div className={`rounded-2xl px-3 py-2 text-sm shadow-sm
                    ${m.side === 'operator' ? 'bg-blue-600 text-white rounded-br-sm' : 'bg-muted text-foreground rounded-bl-sm'}`}>
                    {m.text}
                  </div>
                  {m.side === 'operator' && <ConfidenceDot confidence={m.confidence} />}
                </div>
              </div>
            ))}
            <div ref={chatEndRef} />
          </div>

          <div className="flex gap-2">
            <Button variant="secondary" onClick={teardown} disabled={!connected}>Hang up</Button>
          </div>
        </Card>
      )}
    </>
  )
}

