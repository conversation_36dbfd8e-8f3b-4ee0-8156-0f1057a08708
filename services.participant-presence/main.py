from contextlib import asynccontextmanager
from fastapi import FastAPI

from config import settings
from context import ApplicationContext
from api.v1.internal.router import router as internal_router
from api.v1.presence.router import router as presence_router

@asynccontextmanager
async def lifespan(app: FastAPI):
    context = await ApplicationContext.create()
    app.state.context = context
    yield
    await context.cleanup()

app = FastAPI(
    host=settings.host,
    port=settings.port,
    lifespan=lifespan,
)

app.include_router(internal_router)
app.include_router(presence_router)
