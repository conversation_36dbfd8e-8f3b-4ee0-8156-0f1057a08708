from dataclasses import dataclass

from supabase import create_client, Client

from config import settings


@dataclass
class ApplicationContext:
    """
    Application context for managing shared resources.
    """

    supabase: Client

    @classmethod
    async def create(cls) -> 'ApplicationContext':
        """Create and initialize a new ApplicationContext instance."""
        try:
            # Initialize Supabase client
            supabase_client = create_client(
                settings.supabase_api_url,
                settings.supabase_service_role_key
            )

            # Test the connection to ensure it's working (fail-early)
            try:
                supabase_client.table("user_roles").select("*").execute()
            except Exception as e:
                raise RuntimeError(f"Failed to connect to Supabase: {e}")

            return cls(supabase=supabase_client)
        except Exception as e:
            raise RuntimeError(f"Failed to initialize application context: {e}")

    async def cleanup(self) -> None:
        """Clean up the application context and its resources."""
        try:
            # Supabase client does not require explicit cleanup
            pass
        except Exception as e:
            # Log error but don't raise to avoid issues during shutdown
            print(f"Error during application context cleanup: {e}")

