services:
  user-management:
    build:
      context: .
      dockerfile: services.user-management/Dockerfile
    env_file:
      - .env.supabase.docker

  participant-presence:
    build:
      context: .
      dockerfile: services.participant-presence/Dockerfile
    env_file:
      - .env.supabase.docker

  call-orchestrator:
    build:
      context: .
      dockerfile: services.call-orchestrator/Dockerfile
    env_file:
      - .env.supabase.docker
    depends_on:
      call-orchestrator-init:
        condition: service_completed_successfully

  queuing-service:
    build:
      context: .
      dockerfile: services.queuing-service/Dockerfile

  websocket-bridge:
    build:
      context: .
      dockerfile: services.websocket-bridge/Dockerfile
    env_file:
      - .env.supabase.docker

  voice-router:
    build:
      context: ./services.voice-router
      dockerfile: Dockerfile

  sip-gateway:
    build:
      context: .
      dockerfile: services.sip-gateway/Dockerfile

  ai:
    build:
      context: .
      dockerfile: services.ai/Dockerfile
    environment:
      - WHISPER_DEVICE=cuda
      - WHISPER_COMPUTE_TYPE=float16
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

# ------------------------------------------------------------------

  postgres:
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d cortexa"]
      interval: 5s
      timeout: 5s
      retries: 20
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=cortexa
    env_file:
      - .env.supabase.docker

  call-orchestrator-init:
    build:
      context: .
      dockerfile: services.call-orchestrator/Dockerfile
    env_file:
      - .env.supabase.docker
    environment:
      - DB_URI=postgresql+psycopg://user:password@postgres:5432/cortexa
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
    depends_on:
      postgres:
        condition: service_healthy
    command: >
      sh -lc '
      echo "Running database migrations...";
      alembic upgrade head || echo "Upgrade completed with warnings.";
      echo "Migration setup complete.";
      '
    restart: "no"
    networks:
      - api-gateway