{"name": "h264-profile-level-id", "version": "2.2.3", "description": "TypeScript utility to process H264 profile-level-id values", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://inakibaz.me)", "license": "ISC", "repository": {"type": "git", "url": "https://github.com/versatica/h264-profile-level-id.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mediasoup"}, "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["npm-scripts.mjs", "lib"], "engines": {"node": ">=18"}, "keywords": ["webrtc", "rtp", "h264", "browser", "nodejs"], "scripts": {"prepare": "node npm-scripts.mjs prepare", "typescript:build": "node npm-scripts.mjs typescript:build", "typescript:watch": "node npm-scripts.mjs typescript:watch", "lint": "node npm-scripts.mjs lint", "format": "node npm-scripts.mjs format", "test": "node npm-scripts.mjs test", "coverage": "node npm-scripts.mjs coverage", "release:check": "node npm-scripts.mjs release:check", "release": "node npm-scripts.mjs release"}, "dependencies": {"debug": "^4.4.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/debug": "^4.1.12", "@types/jest": "^30.0.0", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-prettier": "^5.5.4", "globals": "^16.3.0", "jest": "^30.0.5", "open-cli": "^8.0.0", "prettier": "^3.6.2", "ts-jest": "^29.4.1", "typescript": "^5.8.3", "typescript-eslint": "^8.39.1"}}