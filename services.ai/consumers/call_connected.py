import asyncio
from typing import Callable, <PERSON>waitable, <PERSON><PERSON>

from cortexacommon.events.consumer import EventConsumer
from cortexacommon.logging import get_logger
from cortexacommon.events.schemas import CallConnectedEvent

from config import settings


logger = get_logger(__name__)

TOPIC = "call.connected"

async def setup_call_connected_consumer(
    handler: Callable[[CallConnectedEvent], Awaitable[None]]
) -> <PERSON><PERSON>[EventConsumer, asyncio.Task]:
    """
    Creates, starts, and begins consuming from the call.connected topic.

    The provided handler is invoked with the parsed event object.
    """
    consumer = EventConsumer(
        kafka_settings=settings.kafka,
    )

    async def _handle_payload(payload: dict) -> None:
        """Parses the raw message payload and calls the registered handler."""
        event = CallConnectedEvent(**payload)
        logger.info(f"Received {TOPIC} event: {event.call_id}")
        await handler(event)

    consumer.register_handler(TOPIC, _handle_payload)

    await consumer.start()

    # Start consuming in a background task
    consume_task = asyncio.create_task(consumer.consume())
    logger.info(f"Consumer for topic '{TOPIC}' initialized and running.")
    return consumer, consume_task
