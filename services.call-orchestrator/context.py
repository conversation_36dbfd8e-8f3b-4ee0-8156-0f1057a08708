from dataclasses import dataclass

from cortexacommon.logging import get_logger
from cortexacommon.clients.sip_gateway import SIPGatewayService
from cortexacommon.clients.voice_router import VoiceRouterClient

from config import settings
from core.db import Database
from core.call.controller import CallController

logger = get_logger(__name__)


@dataclass
class ApplicationContext:
    """
    Application context for managing shared resources.

    Manages Kafka consumers and external service clients, plus the database engine
    and session factory used by repositories/ORM. Also exposes the CallHandler
    wrapper that orchestrates call lifecycle.
    """

    sip_gateway_service: SIPGatewayService
    voice_router_service: VoiceRouterClient
    call_handler: CallController
    database: Database

    @classmethod
    async def create(cls) -> 'ApplicationContext':
        try:
            db = Database(settings.database)
            await db.initialize()

            logger.info(f"Settings are: {settings}")
            sip = SIPGatewayService(settings.sip_gateway_url)
            voice = VoiceRouterClient(settings.voice_router_url)

            call_handler = await CallController.create(
                db=db, sip_gateway=sip, voice_router=voice,
            )

            return cls(
                sip_gateway_service=sip,
                voice_router_service=voice,
                call_handler=call_handler,
                database=db,
            )
        except Exception as e:
            raise RuntimeError(f"Failed to initialize application context: {e}")

    async def cleanup(self) -> None:
        try:
            if self.database:
                await self.database.cleanup()
            if self.call_handler:
                await self.call_handler.cleanup()
        except Exception as e:
            print(f"Error during application context cleanup: {e}")
