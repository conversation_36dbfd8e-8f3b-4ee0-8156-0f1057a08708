// This file is generated automatically by Next.js
// Do not edit this file manually

type AppRoutes = "/" | "/admin" | "/auth/error" | "/auth/forgot-password" | "/auth/login" | "/auth/sign-up" | "/auth/sign-up-success" | "/auth/update-password" | "/manager" | "/operator"
type AppRouteHandlerRoutes = "/auth/confirm"
type PageRoutes = never
type LayoutRoutes = "/" | "/admin" | "/manager" | "/operator"
type RedirectRoutes = never
type RewriteRoutes = never
type Routes = AppRoutes | PageRoutes | LayoutRoutes | RedirectRoutes | RewriteRoutes | AppRouteHandlerRoutes


interface ParamMap {
  "/": {}
  "/admin": {}
  "/auth/confirm": {}
  "/auth/error": {}
  "/auth/forgot-password": {}
  "/auth/login": {}
  "/auth/sign-up": {}
  "/auth/sign-up-success": {}
  "/auth/update-password": {}
  "/manager": {}
  "/operator": {}
}


export type ParamsOf<Route extends Routes> = ParamMap[Route]

interface LayoutSlotMap {
  "/": never
  "/admin": never
  "/manager": never
  "/operator": never
}


export type { AppRoutes, PageRoutes, LayoutRoutes, RedirectRoutes, RewriteRoutes, ParamMap, AppRouteHandlerRoutes }

declare global {
  /**
   * Props for Next.js App Router page components
   * @example
   * ```tsx
   * export default function Page(props: PageProps<'/blog/[slug]'>) {
   *   const { slug } = await props.params
   *   return <div>Blog post: {slug}</div>
   * }
   * ```
   */
  interface PageProps<AppRoute extends AppRoutes> {
    params: Promise<ParamMap[AppRoute]>
    searchParams: Promise<Record<string, string | string[] | undefined>>
  }

  /**
   * Props for Next.js App Router layout components
   * @example
   * ```tsx
   * export default function Layout(props: LayoutProps<'/dashboard'>) {
   *   return <div>{props.children}</div>
   * }
   * ```
   */
  type LayoutProps<LayoutRoute extends LayoutRoutes> = {
    params: Promise<ParamMap[LayoutRoute]>
    children: React.ReactNode
  } & {
    [K in LayoutSlotMap[LayoutRoute]]: React.ReactNode
  }

  /**
   * Context for Next.js App Router route handlers
   * @example
   * ```tsx
   * export async function GET(request: NextRequest, context: RouteContext<'/api/users/[id]'>) {
   *   const { id } = await context.params
   *   return Response.json({ id })
   * }
   * ```
   */
  interface RouteContext<AppRouteHandlerRoute extends AppRouteHandlerRoutes> {
    params: Promise<ParamMap[AppRouteHandlerRoute]>
  }
}
