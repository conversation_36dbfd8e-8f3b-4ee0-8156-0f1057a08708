import httpx
from typing import List, Dict, Any

from cortexacommon.logging import get_logger

logger = get_logger(__name__)


class VoiceRouterClient:
    """
    Shared client for interacting with the Voice Router internal API.

    Exposes a generic `attach_targets` for arbitrary labeled forks, and a
    backward-compatible `attach` that handles the common caller/operator case.
    """

    def __init__(self, base_url: str):
        self._base_url = base_url.rstrip("/")
        self._client = httpx.AsyncClient(base_url=self._base_url)

    async def attach_targets(self, media_session_id: str, targets: List[Dict[str, Any]]) -> list:
        """
        Attach one or more labeled RTP targets to a mediasoup session.
        Each target must include: {"label": str, "ip": str, "port": int}
        Returns the SSRC mapping from the voice-router.
        """
        response = await self._client.post(
            f"/internal/sessions/{media_session_id}/attach",
            json={"targets": targets},
            timeout=5.0,
        )
        response.raise_for_status()
        data = response.json()
        return data.get("ssrcMapping", [])
    
    async def internal_create_media_session(self, timeout: float = 5.0) -> str:
        """
        Create a new media session.

        Calls POST /internal/sessions and returns the mediaSessionId from the response.

        Args:
            timeout: Request timeout in seconds.

        Returns:
            The created media session ID.
        """
        response = await self._client.post(
            "/internal/sessions",
            json={},
            timeout=timeout
        )

        logger.info(f"create_media_session response: {response.text}")
        response.raise_for_status()
        

        response_data = response.json()
        return response_data["mediaSessionId"]

    async def internal_delete_media_session(self, media_session_id: str, timeout: float = 5.0) -> None:
        """
        Deletes a specific media session.

        Calls DELETE /internal/sessions/{media_session_id} which returns a 204 No Content.

        Args:
            media_session_id: The ID of the session to delete.
            timeout: Request timeout in seconds.
        """
        response = await self._client.delete(
            f"/internal/sessions/{media_session_id}",
            timeout=timeout
        )

        response.raise_for_status()
