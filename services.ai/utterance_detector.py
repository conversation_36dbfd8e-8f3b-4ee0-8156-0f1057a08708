import asyncio
from typing import Callable, Awaitable
from dataclasses import dataclass, field

from cortexacommon.logging import get_logger
from providers.base import BaseVADProvider

logger = get_logger(__name__)

# Type hint for the callback function
UtteranceCallback = Callable[[bytes, str, str], Awaitable[None]]

@dataclass
class UtteranceDetector:
    """
    Detects speech utterances in a real-time audio stream.

    This class buffers incoming audio packets, uses a VAD to monitor for
    speech, and calls a registered callback with the complete audio chunk
    for a single utterance.
    """
    vad: BaseVADProvider
    on_utterance: UtteranceCallback
    flush_timeout_s: float = 0.7
    threshold_bytes: int = 9600

    _audio_buffers: dict[str, bytearray] = field(default_factory=dict, init=False)
    _flush_timers: dict[str, asyncio.Task] = field(default_factory=dict, init=False)

    async def _process_and_yield_utterance(self, participant_id: str, call_id: str) -> None:
        """Internal method to process the buffer and call the callback."""
        buffer = self._audio_buffers.get(participant_id)
        if not buffer:
            return

        processing_data = bytes(buffer)
        buffer.clear()

        if self.vad.is_speech(processing_data):
            logger.info(f"Utterance detected for {participant_id} (len={len(processing_data)} bytes).")
            await self.on_utterance(processing_data, participant_id, call_id)
        else:
            logger.debug(f"Discarding silent buffer for {participant_id}.")

    async def _flush_buffer_after_delay(self, participant_id: str, call_id: str):
        """Timer coroutine to flush the buffer after a period of silence."""
        await asyncio.sleep(self.flush_timeout_s)
        await self._process_and_yield_utterance(participant_id, call_id)
        self._flush_timers.pop(participant_id, None)

    async def handle_packet(self, pcm_data: bytes, participant_id: str, call_id: str) -> None:
        """
        Main entry point to process an incoming audio packet.
        This method now matches the BaseAudioPacketHandler protocol.
        """
        if existing_timer := self._flush_timers.pop(participant_id, None):
            existing_timer.cancel()

        buffer = self._audio_buffers.setdefault(participant_id, bytearray())
        buffer.extend(pcm_data)

        if len(buffer) >= self.threshold_bytes:
            await self._process_and_yield_utterance(participant_id, call_id)
        else:
            self._flush_timers[participant_id] = asyncio.create_task(
                self._flush_buffer_after_delay(participant_id, call_id)
            )

    def cleanup_participant(self, participant_id: str):
        """Cleans up resources for a specific participant."""
        logger.info(f"Cleaning up UtteranceDetector for participant {participant_id}")
        self._audio_buffers.pop(participant_id, None)
        if timer := self._flush_timers.pop(participant_id, None):
            timer.cancel()