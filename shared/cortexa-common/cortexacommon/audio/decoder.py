import av

from cortexacommon.logging import get_logger

logger = get_logger(__name__)


class OpusDecoderResampler:
    """
    Thin wrapper around PyAV to decode Opus packets and resample to PCM S16LE mono.

    Usage:
        dec = OpusDecoderResampler(target_sample_rate=16000)
        pcm = dec.decode(opus_payload_bytes)
    """

    def __init__(
        self,
        target_sample_rate: int = 16000,
        channels: int = 1,
        sample_format: str = "s16",
        layout: str = "mono",
    ) -> None:
        self._target_rate = target_sample_rate
        self._channels = channels
        self._decoder = av.CodecContext.create("opus", "r")
        self._resampler = av.AudioResampler(format=sample_format, layout=layout, rate=target_sample_rate)

    def decode(self, opus_payload: bytes) -> bytes:
        """
        Decode a single Opus payload to PCM bytes at the configured sample rate.
        Returns empty bytes if decoding produced no frames.
        """
        try:
            packet = av.Packet(opus_payload)
            frames = self._decoder.decode(packet)  # pyright: ignore[reportAttributeAccessIssue]
            if not frames:
                logger.info(f"Opus decode produced no frames for payload len={len(opus_payload)}")
                return b""

            out = bytearray()
            for frame in frames:
                resampled_frames = self._resampler.resample(frame)
                for rf in resampled_frames:
                    out.extend(rf.to_ndarray().tobytes())
            return bytes(out)
        except Exception:
            logger.warning("Opus decode failed; dropping packet", exc_info=True)
            return b""
    def close(self) -> None:
        # PyAV objects are GC'd; nothing explicit needed here.
        pass

