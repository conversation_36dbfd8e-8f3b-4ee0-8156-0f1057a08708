{"name": "fake-mediastreamtrack", "version": "2.1.4", "description": "Fake W3C MediaStreamTrack implementation", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://inakibaz.me)", "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/ibc/fake-mediastreamtrack.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}, "files": ["LICENSE", "README.md", "npm-scripts.mjs", "lib"], "engines": {"node": ">=18"}, "scripts": {"prepare": "node npm-scripts.mjs prepare", "typescript:build": "node npm-scripts.mjs typescript:build", "typescript:watch": "node npm-scripts.mjs typescript:watch", "lint": "node npm-scripts.mjs lint", "format": "node npm-scripts.mjs format", "test": "node npm-scripts.mjs test", "coverage": "node npm-scripts.mjs coverage", "release:check": "node npm-scripts.mjs release:check", "release": "node npm-scripts.mjs release"}, "dependencies": {"uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.34.0", "@types/jest": "^30.0.0", "@types/uuid": "^10.0.0", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-prettier": "^5.5.4", "globals": "^16.3.0", "jest": "^30.1.0", "open-cli": "^8.0.0", "prettier": "^3.6.2", "ts-jest": "^29.4.1", "typescript": "^5.9.2", "typescript-eslint": "^8.41.0"}}