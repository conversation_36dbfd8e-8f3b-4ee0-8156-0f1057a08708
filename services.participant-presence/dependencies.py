from starlette.requests import HTTPConnection
from cortexacommon.security import Security

from context import ApplicationContext
from config import settings


def get_app_context_dependency(conn: HTTPConnection) -> ApplicationContext:
    """FastAPI dependency to get the application context from app.state."""
    return conn.app.state.context


async def get_security_dependency() -> Security:
    """FastAPI dependency to get the security instance."""
    return Security(settings.security)
