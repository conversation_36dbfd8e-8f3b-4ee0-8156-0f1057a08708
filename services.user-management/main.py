from contextlib import asynccontextmanager

from fastapi import FastAPI
from loguru import logger

from context import ApplicationContext
from config import settings
from api.users.user_routes import router as user_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan context manager.

    Handles initialization and cleanup of application resources.
    """
    # Startup
    context = await ApplicationContext.create()
    app.state.context = context

    yield

    # Shutdown
    await context.cleanup()

logger.info("User Management Service starting...")
logger.info(f"Settings: {settings}")

app = FastAPI(
    title="Cortexa User Management Service",
    description="API for managing user roles and creation.",
    version="1.0.0",
    lifespan=lifespan,
    host=settings.host,
    port=settings.port,
)

# Include routers
app.include_router(user_router)
