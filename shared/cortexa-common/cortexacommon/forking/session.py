import asyncio
from typing import Callable, Dict, List, Optional

from cortexacommon.logging import get_logger
from cortexacommon.rtp.server import RtpServerProtocol
from cortexacommon.clients.voice_router import VoiceRouterClient

logger = get_logger(__name__)


OnPacketCb = Callable[[str, int, bytes], None]
ParticipantPacketCb = Callable[[str, bytes], None]


class AudioForkingSession:
    """
    Common abstraction to:
    - open UDP RTP listeners per label (e.g., caller/operator)
    - attach Voice Router to fork media to those listeners
    - forward parsed RTP payloads via a simple callback: on_packet(label, ssrc, payload)

    This hides socket, RTP parsing, and voice-router API details from services.
    """

    def __init__(self, service_ip: str, voice_router_client: VoiceRouterClient):
        self._service_ip = service_ip
        self._vr = voice_router_client
        self._transports: Dict[str, asyncio.DatagramTransport] = {}
        self._labels: List[str] = []
        self._on_packet: Optional[OnPacketCb] = None
        self._on_participant_packet: Optional[ParticipantPacketCb] = None
        self._participant_by_label: Optional[Dict[str, str]] = None
        self._ssrc_to_label: Dict[int, str] = {}
        self._label_to_ssrc: Dict[str, int] = {}
        self._started = False

    def _make_handler(self, label: str) -> Callable[[int, bytes], None]:
        def handler(ssrc: int, payload: bytes) -> None:
            # If we have an SSRC mapping, validate this packet matches the expected label
            expected = self._ssrc_to_label.get(ssrc)
            if expected and expected != label:
                logger.warning(f"Dropping RTP packet: SSRC {ssrc} mapped to {expected} but arrived on {label}")
                return
            try:
                if self._on_participant_packet and self._participant_by_label:
                    participant_id = self._participant_by_label.get(label)
                    if participant_id:
                        self._on_participant_packet(participant_id, payload)
                        return
                if self._on_packet:
                    self._on_packet(label, ssrc, payload)
            except Exception:
                logger.exception(f"Error in packet callback for label {label}")
        return handler

    async def start(
        self,
        media_session_id: str,
        labels: List[str],
        on_packet: Optional[OnPacketCb] = None,
        on_participant_packet: Optional[ParticipantPacketCb] = None,
        participant_by_label: Optional[Dict[str, str]] = None,
    ) -> List[dict]:
        if self._started:
            raise RuntimeError("AudioForkingSession already started")
        self._labels = labels[:]
        self._on_packet = on_packet
        self._on_participant_packet = on_participant_packet
        self._participant_by_label = participant_by_label

        # 1) Create UDP listeners per label
        loop = asyncio.get_running_loop()
        targets = []
        for label in labels:
            transport, _ = await loop.create_datagram_endpoint(
                lambda l=label: RtpServerProtocol(packet_handler=self._make_handler(l)),
                local_addr=("0.0.0.0", 0),
            )
            self._transports[label] = transport
            addr = transport.get_extra_info("sockname")
            logger.info(f"RTP listener for {label} created at {addr}")
            targets.append({"label": label, "ip": self._service_ip, "port": addr[1]})

        # 2) Ask Voice Router to fork to our listeners
        ssrc_mapping = await self._vr.attach_targets(media_session_id, targets)
        logger.info(f"SSRC mapping received from voice-router: {ssrc_mapping}")

        # 3) Store SSRC mapping for validation and future lookups
        self._ssrc_to_label.clear()
        self._label_to_ssrc.clear()
        for m in ssrc_mapping:
            label = m.get("label")
            ssrc = m.get("ssrc")
            if label is not None and ssrc is not None:
                self._ssrc_to_label[ssrc] = label
                self._label_to_ssrc[label] = ssrc

        self._started = True
        return ssrc_mapping

    async def stop(self) -> None:
        if not self._started:
            return
        logger.info(f"Stopping AudioForkingSession with labels: {self._labels}")
        for t in self._transports.values():
            try:
                t.close()
            except Exception:
                logger.exception("Failed closing transport")
        self._transports.clear()
        self._started = False
